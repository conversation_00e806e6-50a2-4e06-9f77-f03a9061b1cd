/**
 * 用户认证状态切片
 */
import { create } from 'zustand';
import { createClient } from '@/utils/supabase/client';
import { getCurrentUser, signIn as userSignIn, signUp as userSignUp, signOut as userSignOut } from '@/services/userService';
import type { User, Session } from '@supabase/supabase-js';

// 认证状态接口
interface AuthState {
  user: User | null;
  session: Session | null;
  isLoading: boolean;
  error: string | null;
}

// 创建Supabase客户端
const supabase = createClient();

interface AuthStore extends AuthState {
  // 初始化用户状态
  initialize: () => Promise<void>;

  // 用户登录
  signIn: (email: string, password: string) => Promise<void>;

  // 用户注册
  signUp: (email: string, password: string, userId: string) => Promise<{ user: User | null; session: Session | null; needsEmailConfirmation?: boolean }>;

  // 用户登出
  signOut: () => Promise<void>;

  // 设置用户
  setUser: (user: User | null) => void;

  // 设置会话
  setSession: (session: Session | null) => void;
}

/**
 * 用户认证状态
 */
export const useAuthStore = create<AuthStore>((set, get) => {
  // 监听认证状态变化
  supabase.auth.onAuthStateChange((event, session) => {
    console.log('Auth state changed:', event, session?.user?.email);

    if (event === 'SIGNED_IN' && session) {
      set({ user: session.user, session, isLoading: false, error: null });
    } else if (event === 'SIGNED_OUT') {
      set({ user: null, session: null, isLoading: false, error: null });
    } else if (event === 'TOKEN_REFRESHED' && session) {
      set({ user: session.user, session, error: null });
    }
  });

  return {
    user: null,
    session: null,
    isLoading: true,
    error: null,

    // 初始化用户状态
  initialize: async () => {
    try {
      // 检查当前状态
      const currentState = get();

      // 如果已经有用户，快速返回
      if (currentState.user) {
        set({ isLoading: false });
        return;
      }

      set({ isLoading: true, error: null });

      // 获取当前session和用户
      const { data: { session }, error } = await supabase.auth.getSession();

      if (error) {
        console.error('获取session失败:', error);
        set({ user: null, session: null, isLoading: false });
        return;
      }

      if (session?.user) {
        set({ user: session.user, session, isLoading: false });
      } else {
        set({ user: null, session: null, isLoading: false });
      }
    } catch (error) {
      console.error('初始化用户状态失败:', error);
      set({
        user: null,
        session: null,
        isLoading: false,
        error: error instanceof Error ? error.message : '初始化用户状态失败'
      });
    }
  },

  // 用户登录
  signIn: async (email: string, password: string) => {
    try {
      set({ isLoading: true, error: null });
      const result = await userSignIn(email, password);
      // 设置用户和会话信息
      set({
        user: result.user,
        session: result.session,
        isLoading: false
      });
    } catch (error) {
      console.error('登录失败:', error);
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : '登录失败'
      });
      throw error;
    }
  },

  // 用户注册
  signUp: async (email: string, password: string, userId: string) => {
    try {
      set({ isLoading: true, error: null });
      const result = await userSignUp(email, password, userId);
      // 注册后不设置用户状态，需要邮箱验证
      set({ isLoading: false });
      return result;
    } catch (error) {
      console.error('注册失败:', error);
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : '注册失败'
      });
      throw error;
    }
  },

  // 用户登出
  signOut: async () => {
    try {
      set({ isLoading: true, error: null });
      await userSignOut();
      set({ user: null, session: null, isLoading: false });
    } catch (error) {
      console.error('登出失败:', error);
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : '登出失败'
      });
      throw error;
    }
  },

  // 设置用户
  setUser: (user: User | null) => {
    set({ user });
  },

  // 设置会话
  setSession: (session: Session | null) => {
    set({ session });
  }
  };
});
