/**
 * 用户认证钩子
 */
import { useEffect } from 'react';
import { useAuthStore } from '@/store/slices/authSlice';

/**
 * 用户认证钩子
 * @returns 用户认证状态和方法
 */
export const useAuth = () => {
  const {
    user,
    session,
    isLoading,
    error,
    initialize,
    signIn,
    signUp,
    signOut,
    setUser,
    setSession
  } = useAuthStore();

  // 初始化用户状态
  useEffect(() => {
    initialize();
  }, [initialize]);

  return {
    user,
    session,
    isLoading,
    error,
    signIn,
    signUp,
    signOut,
    isAuthenticated: !!user
  };
};
