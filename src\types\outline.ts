/**
 * 大纲相关类型定义
 */

// 大纲数据结构
export interface Outline {
  id: string;
  title: string;
  content: string;
  order: number;
  workId: number;
  createdAt: Date;
  updatedAt: Date;
}

// 大纲表单数据
export interface OutlineFormData {
  title: string;
  content: string;
}

// 大纲模态窗口属性
export interface OutlineModalProps {
  isOpen: boolean;
  onClose: () => void;
  workId: number;
}

// 大纲编辑器属性
export interface OutlineEditorProps {
  outline: Outline;
  workId: number;
  onSave: (outline: Outline) => void;
  isSaving: boolean;
  lastSavedAt: Date | null;
}
