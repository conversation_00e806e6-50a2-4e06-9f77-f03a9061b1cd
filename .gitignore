# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# package manager lock files (keep only package-lock.json for npm)
pnpm-lock.yaml
yarn.lock

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env
.env*.local
.env.example

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts