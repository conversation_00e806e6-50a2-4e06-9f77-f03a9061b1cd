/**
 * 用户认证状态管理API
 */
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

// 内部API密钥验证
const INTERNAL_API_KEY = "verification_internal_2024_secure_key_abc123";

// GET - 查询用户认证状态
export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: '未提供认证令牌' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const { data: { user }, error } = await supabase.auth.getUser(token);

    if (error || !user) {
      return NextResponse.json({ error: '用户未登录' }, { status: 401 });
    }

    // 查询用户认证状态
    const { data, error: queryError } = await supabase
      .from('membership-look')
      .select('is_verified, daily_free_quota, daily_free_used, last_free_reset_date')
      .eq('user_id', user.id)
      .single();

    if (queryError) {
      return NextResponse.json({ error: '查询失败' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      data: {
        is_verified: data?.is_verified || false,
        daily_free_quota: data?.daily_free_quota || 0,
        daily_free_used: data?.daily_free_used || 0,
        daily_free_remaining: Math.max(0, (data?.daily_free_quota || 0) - (data?.daily_free_used || 0)),
        last_free_reset_date: data?.last_free_reset_date
      }
    });
  } catch (error) {
    return NextResponse.json({ error: '服务器错误' }, { status: 500 });
  }
}

// PUT - 设置用户认证状态（仅内部调用）
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { internalApiKey, userUuid, verified, freeQuota = 50 } = body;

    // 内部API密钥验证
    if (!internalApiKey || internalApiKey !== INTERNAL_API_KEY) {
      return NextResponse.json({ error: '无效的内部API密钥' }, { status: 403 });
    }

    // 调用数据库函数设置认证状态
    const { data, error } = await supabase
      .rpc('set_user_verification_status', {
        user_uuid: userUuid,
        verified_status: verified,
        free_quota: freeQuota
      });

    if (error || !data) {
      return NextResponse.json({ error: '设置认证状态失败' }, { status: 500 });
    }

    return NextResponse.json({ success: true, message: '认证状态设置成功' });
  } catch (error) {
    return NextResponse.json({ error: '服务器错误' }, { status: 500 });
  }
}
