/**
 * 知识库仓库
 */
import { Knowledge, Archive } from '../types/knowledge';
import { dbOperations } from '../core/operations';
import { DB_CONFIG } from '../config';

const { MAIN } = DB_CONFIG.NAMES;
const { KNOWLEDGE } = DB_CONFIG.STORES.MAIN;

/**
 * 添加知识（兼容性函数）
 * @param archive 知识数据
 * @returns 添加后的知识
 */
export const addArchive = async (archive: Omit<Archive, 'id'>): Promise<Archive> => {
  return dbOperations.add<Archive>(MAIN, KNOWLEDGE, archive);
};

/**
 * 获取所有知识（兼容性函数）
 * @returns 所有知识
 */
export const getAllArchives = async (): Promise<Archive[]> => {
  return dbOperations.getAll<Archive>(MAIN, KNOWLEDGE);
};



/**
 * 根据作品ID获取知识（兼容性函数）
 * @param workId 作品ID
 * @returns 指定作品的知识
 */
export const getArchivesByWorkId = async (workId: number): Promise<Archive[]> => {
  try {
    const archives = await dbOperations.getAll<Archive>(MAIN, KNOWLEDGE);
    return archives.filter(archive => archive.workId === workId);
  } catch (error) {
    console.error(`获取作品ID为${workId}的知识失败:`, error);
    return [];
  }
};

/**
 * 根据ID获取知识（兼容性函数）
 * @param id 知识ID
 * @returns 知识或null
 */
export const getArchiveById = async (id: number): Promise<Archive | null> => {
  const archive = await dbOperations.getById<Archive>(MAIN, KNOWLEDGE, id);
  return archive || null;
};

/**
 * 更新知识（兼容性函数）
 * @param archive 知识数据
 * @returns 更新后的知识
 */
export const updateArchive = async (archive: Archive): Promise<Archive> => {
  if (!archive.id) throw new Error('Archive ID is required');
  return dbOperations.update<Archive & { id: number }>(MAIN, KNOWLEDGE, archive as Archive & { id: number });
};

/**
 * 删除知识（兼容性函数）
 * @param id 知识ID
 */
export const deleteArchive = async (id: number): Promise<void> => {
  return dbOperations.remove(MAIN, KNOWLEDGE, id);
};

// 新的知识库函数
/**
 * 添加知识
 * @param knowledge 知识
 * @returns 添加后的知识
 */
export const addKnowledge = async (knowledge: Omit<Knowledge, 'id'>): Promise<Knowledge> => {
  return dbOperations.add<Knowledge>(MAIN, KNOWLEDGE, knowledge);
};

/**
 * 获取所有知识
 * @returns 所有知识
 */
export const getAllKnowledge = async (): Promise<Knowledge[]> => {
  return dbOperations.getAll<Knowledge>(MAIN, KNOWLEDGE);
};



/**
 * 根据作品ID获取知识
 * @param workId 作品ID
 * @returns 指定作品的知识
 */
export const getKnowledgeByWorkId = async (workId: number): Promise<Knowledge[]> => {
  try {
    const knowledgeItems = await dbOperations.getAll<Knowledge>(MAIN, KNOWLEDGE);
    return knowledgeItems.filter(item => item.workId === workId);
  } catch (error) {
    console.error(`获取作品ID为${workId}的知识失败:`, error);
    return [];
  }
};

/**
 * 根据ID获取知识
 * @param id 知识ID
 * @returns 知识或null
 */
export const getKnowledgeById = async (id: number): Promise<Knowledge | null> => {
  const knowledge = await dbOperations.getById<Knowledge>(MAIN, KNOWLEDGE, id);
  return knowledge || null;
};

/**
 * 更新知识
 * @param knowledge 知识
 * @returns 更新后的知识
 */
export const updateKnowledge = async (knowledge: Knowledge): Promise<Knowledge> => {
  if (!knowledge.id) throw new Error('Knowledge ID is required');
  return dbOperations.update<Knowledge & { id: number }>(MAIN, KNOWLEDGE, knowledge as Knowledge & { id: number });
};

/**
 * 删除知识
 * @param id 知识ID
 */
export const deleteKnowledge = async (id: number): Promise<void> => {
  return dbOperations.remove(MAIN, KNOWLEDGE, id);
};
