'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import { validateEmailDomain } from '@/lib/utils/validators';

/**
 * 忘记密码页面
 */
export default function ForgotPasswordPage() {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 如果已登录，跳转到首页
  useEffect(() => {
    if (isAuthenticated && !isLoading) {
      router.push('/');
    }
  }, [isAuthenticated, isLoading, router]);

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setIsSubmitting(true);

    try {
      // 验证邮箱格式
      const emailError = validateEmailDomain(email);
      if (emailError) {
        setError(emailError);
        setIsSubmitting(false);
        return;
      }

      // 发送密码重置OTP
      const response = await fetch('/api/user/reset-password-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || '发送验证码失败');
      }

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.message || '发送验证码失败');
      }

      // 发送成功，跳转到重置密码OTP页面
      router.push(`/reset-password-otp?email=${encodeURIComponent(email)}`);

    } catch (error) {
      console.error('发送密码重置验证码失败:', error);
      setError(error instanceof Error ? error.message : '发送验证码失败，请重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  // 如果正在加载或已登录，显示加载中
  if (isLoading || isAuthenticated) {
    return (
      <div className="min-h-screen bg-bg-color flex flex-col">
        <div className="flex-1 flex items-center justify-center">
          <div className="animate-pulse text-primary-green">
            <svg className="animate-spin h-10 w-10 text-primary-green" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-bg-color flex items-center justify-center">
      <div className="w-full max-w-md">
        <div className="ghibli-card p-8 relative">
          <h2 className="text-2xl font-bold text-center mb-6 text-text-dark">
            忘记密码
          </h2>

          <div className="text-center mb-6">
            <p className="text-text-medium text-sm">
              请输入您的注册邮箱，我们将向您发送6位验证码来重置密码。
            </p>
          </div>

          {error && (
            <div className="bg-red-50 text-red-600 p-3 rounded-lg mb-4 text-sm">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit}>
            <div className="mb-6">
              <label className="block text-text-medium text-sm font-medium mb-2" htmlFor="email">
                邮箱地址
              </label>
              <input
                id="email"
                type="email"
                className="w-full px-4 py-2 bg-white bg-opacity-70 border border-[rgba(120,180,140,0.3)] rounded-xl focus:outline-none focus:ring-2 focus:ring-[rgba(120,180,140,0.5)] transition-all duration-200 text-text-dark"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="请输入您的邮箱地址"
                required
              />
              <p className="text-xs text-text-light mt-1">
                支持 @qq.com、@163.com 和 @gmail.com 邮箱
              </p>
            </div>

            <button
              type="submit"
              className="w-full bg-primary-green text-white py-2 px-4 rounded-full hover:bg-[#4a8d5b] transition-colors duration-200 flex items-center justify-center mb-4"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <span className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  发送中...
                </span>
              ) : '发送验证码'}
            </button>

            <div className="text-center">
              <button
                type="button"
                className="text-primary-green hover:underline text-sm"
                onClick={() => router.push('/login')}
              >
                返回登录
              </button>
            </div>
          </form>

          <div className="mt-6 p-4 bg-amber-50 rounded-lg">
            <div className="flex items-start">
              <svg className="w-5 h-5 text-amber-500 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <div>
                <p className="text-amber-700 text-xs font-medium mb-1">安全提示</p>
                <p className="text-amber-600 text-xs">
                  验证码有效期为10分钟，请及时使用。如果您没有申请重置密码，请忽略此邮件。
                </p>
              </div>
            </div>
          </div>

          <div className="page-curl"></div>
        </div>
      </div>
    </div>
  );
}
