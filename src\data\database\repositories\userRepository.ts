/**
 * 用户仓库
 */
import {
  getCurrentUser as getUser,
  updateUserProfile as updateProfile
} from '@/services/userService';
import { User } from '../types/user';

/**
 * 获取当前用户
 * @returns 当前用户或null
 */
export const getCurrentUser = async (): Promise<User | null> => {
  try {
    const user = await getUser();
    return user;
  } catch (error) {
    console.error('获取当前用户失败:', error);
    return null;
  }
};

/**
 * 更新用户资料
 * @param userData 用户资料
 * @returns 更新后的用户
 */
export const updateUserProfile = async (userData: { name?: string; display_name?: string; avatar_url?: string }) => {
  try {
    // 如果设置了name但没有设置display_name，自动同步
    if (userData.name && !userData.display_name) {
      userData.display_name = userData.name;
    }

    const user = await updateProfile(userData);
    return user;
  } catch (error) {
    console.error('更新用户资料失败:', error);
    throw error;
  }
};

/**
 * 更新用户邮箱
 * @param email 新邮箱
 * @returns 更新结果
 */
export const updateUserEmail = async (email: string) => {
  try {
    const user = await updateProfile({ email });
    return { user };
  } catch (error) {
    console.error('更新用户邮箱失败:', error);
    throw error;
  }
};


