/**
 * 文件删除API
 * 处理删除MinIO中的文件和数据库记录
 */
import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { deleteFile } from '@/lib/minio';

// Supabase配置
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

/**
 * 服务器端获取当前用户
 */
async function getCurrentUser(request: NextRequest) {
  try {
    const supabaseServer = createServerClient(
      supabaseUrl,
      supabaseAnonKey,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll().map(cookie => ({
              name: cookie.name,
              value: cookie.value
            }));
          },
          setAll() {
            // 在API路由中不需要设置cookies
          },
        },
      }
    );

    const { data: { user }, error } = await supabaseServer.auth.getUser();

    if (error) {
      console.error('获取用户失败:', error);
      return null;
    }

    return user;
  } catch (error) {
    console.error('获取用户异常:', error);
    return null;
  }
}

/**
 * DELETE 方法 - 删除文件
 */
export async function DELETE(request: NextRequest) {
  try {
    // 获取当前用户
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json(
        { error: '用户未登录' },
        { status: 401 }
      );
    }

    // 解析请求体
    const body = await request.json();
    const { id, path, workTitle } = body;

    if (!id && !path && !workTitle) {
      return NextResponse.json(
        { error: '缺少必需参数：id、path 或 workTitle' },
        { status: 400 }
      );
    }

    // 创建Supabase客户端
    const supabaseServer = createServerClient(
      supabaseUrl,
      supabaseAnonKey,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll().map(cookie => ({
              name: cookie.name,
              value: cookie.value
            }));
          },
          setAll() {},
        },
      }
    );

    let fileRecord;

    if (id) {
      // 通过文件ID查询
      const { data, error } = await supabaseServer
        .from('novel_files')
        .select('*')
        .eq('id', id)
        .eq('user_id', user.id)
        .single();

      if (error || !data) {
        return NextResponse.json(
          { error: '文件不存在或无权限访问' },
          { status: 404 }
        );
      }

      fileRecord = data;
    } else if (path) {
      // 通过文件路径查询
      const { data, error } = await supabaseServer
        .from('novel_files')
        .select('*')
        .eq('minio_object_key', path)
        .eq('user_id', user.id)
        .single();

      if (error || !data) {
        return NextResponse.json(
          { error: '文件不存在或无权限访问' },
          { status: 404 }
        );
      }

      fileRecord = data;
    } else if (workTitle) {
      // 通过作品标题查询
      const { data, error } = await supabaseServer
        .from('novel_files')
        .select('*')
        .eq('work_title', workTitle)
        .eq('user_id', user.id)
        .single();

      if (error || !data) {
        return NextResponse.json(
          { error: '作品不存在或无权限访问' },
          { status: 404 }
        );
      }

      fileRecord = data;
    }

    if (!fileRecord) {
      return NextResponse.json(
        { error: '文件记录不存在' },
        { status: 404 }
      );
    }

    console.log(`开始删除文件: ${fileRecord.minio_object_key}`);

    // 从MinIO删除文件
    try {
      await deleteFile(fileRecord.minio_object_key);
    } catch (minioError) {
      console.error('从MinIO删除文件失败:', minioError);
      // 继续删除数据库记录，即使MinIO删除失败
    }

    // 从数据库删除记录
    const { error: dbError } = await supabaseServer
      .from('novel_files')
      .delete()
      .eq('id', fileRecord.id)
      .eq('user_id', user.id);

    if (dbError) {
      console.error('删除数据库记录失败:', dbError);
      return NextResponse.json(
        { error: '删除数据库记录失败' },
        { status: 500 }
      );
    }

    console.log(`文件删除成功: ${fileRecord.minio_object_key}`);

    return NextResponse.json({
      success: true,
      message: '文件删除成功',
      data: {
        id: fileRecord.id,
        fileName: fileRecord.file_name,
        filePath: fileRecord.minio_object_key
      }
    });

  } catch (error) {
    console.error('文件删除失败:', error);
    return NextResponse.json(
      { 
        error: '文件删除失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
