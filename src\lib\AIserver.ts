/**
 * AIserver - 简化的AI服务接口
 * 通过中间层提供与AI API的通信功能
 */
import { getCurrentUser } from '@/services/userService';
import { getPromptById } from '@/data';
import { BillingService } from '@/lib/billing';

// 模型常量 - 使用代号而非真实模型名称
export const MODELS = {
  LLM_TEST: 'ceshi', // 测试版
  LLM_CLAUDE: 'kelaode', // 克劳德
};

// 消息类型
export interface Message {
  role: 'user' | 'system' | 'assistant';
  content: string;
}

// Usage类型
export interface Usage {
  prompt_tokens: number;
  completion_tokens: number;
  total_tokens: number;
}

// 生成选项接口
export interface GenerateOptions {
  model: string;
  temperature?: number;
  max_tokens?: number; // 更改为 max_tokens
  stream?: boolean;
  abortSignal?: AbortSignal; // AbortSignal 可能需要不同的处理方式
}

// 默认选项
const DEFAULT_OPTIONS: Omit<GenerateOptions, 'model' | 'abortSignal'> = {
  temperature: 0.7,
  max_tokens: 64000, // 调整默认max_tokens
  stream: true
};

/**
 * 错误处理函数
 */
const handleAIError = (error: any): string => {
  console.error('AI服务错误:', error);
  const errorMessage = error?.message || JSON.stringify(error) || '未知错误'; // 更详细的错误日志

  if (errorMessage.includes('API key not configured')) {
    return '401';
  }

  // 其他错误类型
  if (errorMessage.includes('token') || errorMessage.includes('context_length_exceeded')) {
    return '413';
  }
  if (errorMessage.includes('network') || errorMessage.includes('timeout') || errorMessage.includes('fetch failed')) {
    return '503';
  }
  if (errorMessage.includes('authentication') || errorMessage.includes('认证')) {
     return '401';
  }
  // 默认错误消息
  return '500';
};

/**
 * 解密提示词内容
 * 如果提示词内容是加密的（以__ENCRYPTED_PROMPT_ID__:开头），则解密
 * @param messages 消息数组
 * @returns 解密后的消息数组
 */
const decryptPromptMessages = async (messages: Message[]): Promise<Message[]> => {
  // 创建一个新的消息数组，避免修改原始数组
  const decryptedMessages: Message[] = [];

  for (const message of messages) {
    // 检查是否是系统消息且内容包含__ENCRYPTED_PROMPT_ID__或__USER_PROMPT_ID__
    if (message.role === 'system' && (message.content.includes('__ENCRYPTED_PROMPT_ID__:') || message.content.includes('__USER_PROMPT_ID__:'))) {
      try {
        // 检查是否是新格式（包含<提示词内容>标签）
        const isNewFormat = message.content.includes('<提示词内容>') && message.content.includes('</提示词内容>');

        // 检查是否是用户提示词（用户提示词将在后端 /api/ai/stream 中处理）
        const isUserPrompt = message.content.includes('__USER_PROMPT_ID__:');

        // 所有提示词都直接传递给后端处理
        console.log('检测到提示词，将在后端 /api/ai/stream 中统一处理');
        decryptedMessages.push(message);
      } catch (error) {
        console.error('处理提示词失败:', error);
        // 如果处理失败，使用原始消息
        decryptedMessages.push(message);
      }
    } else {
      // 如果不是系统消息，直接添加
      decryptedMessages.push(message);
    }
  }

  return decryptedMessages;
};



/**
 * AI内容生成核心
 */
export const AIGenerator = {
  /**
   * 生成AI内容(非流式) - 通过流式接口实现
   * @param messages 消息数组
   * @param options 生成选项
   * @returns 生成的内容
   */
  generate: async (
    messages: Message[],
    options: Partial<GenerateOptions> = {}
  ): Promise<string> => {
    if (!messages || messages.length === 0) return "";

    // 确保仅在客户端执行
    if (typeof window === 'undefined') {
      throw new Error('AI generation can only be executed in browser environment');
    }

    return new Promise((resolve, reject) => {
      let result = '';

      // 使用流式接口来实现非流式功能
      AIGenerator.generateStream(
        messages,
        options,
        (chunk) => {
          result += chunk;
        }
      ).then(() => {
        resolve(result);
      }).catch((error) => {
        reject(error);
      });
    });
  },

  /**
   * 生成AI内容(流式) - 通过中间层
   * @param messages 消息数组
   * @param options 生成选项
   * @param onChunk 块回调函数
   * @param onUsage usage回调函数
   */
  generateStream: async (
    messages: Message[],
    options: Partial<GenerateOptions> = {},
    onChunk: (chunk: string) => void,
    onUsage?: (usage: Usage) => void
  ): Promise<void> => {
    if (!messages || messages.length === 0 || typeof onChunk !== 'function') return;

    // 确保仅在客户端执行
    if (typeof window === 'undefined') {
      throw new Error('AI generation can only be executed in browser environment');
    }

    try {
      // 处理提示词内容
      const decryptedMessages = await decryptPromptMessages(messages);

      // 确保 model 有明确的值，避免 undefined
      const modelToUse = options.model || MODELS.LLM_TEST;

      console.log(`前端流式生成使用模型: ${modelToUse}`);

      // 检查用户余额（包括模型权限）
      const user = await getCurrentUser();
      if (user?.id) {
        const canUseResult = await BillingService.canUseAI(user.id, 100, modelToUse); // 预估100字消耗，传递模型代号
        if (!canUseResult.canUse) {
          throw new Error(canUseResult.message || '无法使用AI功能');
        }
        console.log('余额检查通过:', canUseResult.balance === -2 ? '无限制' : `剩余${canUseResult.balance}字`);
      } else {
        console.warn('未获取到用户信息，跳过余额检查');
      }

      // 添加请求信息日志
      console.log("前端发送流式请求:", {
        model: modelToUse,
        messages: decryptedMessages.map(m => ({ role: m.role, content: m.role === 'system' ? '(系统提示词)' : m.content })), // 不在日志中显示完整的系统提示词内容
        temperature: options.temperature || DEFAULT_OPTIONS.temperature
      });

      // 构建请求体
      const requestBody = {
        messages: decryptedMessages.map(m => ({ role: m.role, content: m.content })),
        model: modelToUse,
        temperature: options.temperature || DEFAULT_OPTIONS.temperature,
        max_tokens: options.max_tokens || DEFAULT_OPTIONS.max_tokens
      };

      // 获取认证token
      const { createClient } = await import('@/utils/supabase/client');
      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();

      // 调用中间层API
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      // 添加认证头
      if (session?.access_token) {
        headers['Authorization'] = `Bearer ${session.access_token}`;
      }

      const response = await fetch('/api/ai/stream', {
        method: 'POST',
        headers,
        body: JSON.stringify(requestBody),
        signal: options.abortSignal
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: response.status.toString() }));
        // 优先显示后端返回的错误码，如果没有则显示HTTP状态码
        throw new Error(errorData.error || response.status.toString());
      }

      console.log("前端Stream created successfully");

      // 处理流式响应
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('无法获取响应流');
      }

      const decoder = new TextDecoder();

      try {
        while (true) {
          const { done, value } = await reader.read();

          if (done) {
            break;
          }

          const chunk = decoder.decode(value, { stream: true });
          if (chunk) {
            // 检查是否包含错误消息
            if (chunk.includes('ERROR:')) {
              const errorMatch = chunk.match(/ERROR:\s*(.+)/);
              if (errorMatch) {
                const errorMessage = errorMatch[1].trim();
                console.error("流式响应中收到错误:", errorMessage);
                throw new Error(errorMessage);
              }
            }

            // 检查是否包含usage数据
            if (chunk.includes('__USAGE_DATA__:')) {
              const usageMatch = chunk.match(/__USAGE_DATA__:(.+)/);
              if (usageMatch && onUsage) {
                try {
                  const usageData = JSON.parse(usageMatch[1]);
                  console.log("前端收到usage信息:", usageData);
                  onUsage(usageData);
                } catch (error) {
                  console.error("解析usage数据失败:", error);
                }
              }
              // 移除usage数据，只保留内容部分
              const contentOnly = chunk.replace(/__USAGE_DATA__:.+/, '');
              if (contentOnly) {
                onChunk(contentOnly);
              }
            } else {
              // 直接传递字符，后端已经逐字符发送
              onChunk(chunk);
            }
          }
        }
      } finally {
        reader.releaseLock();
      }


    } catch (error: any) {
      console.error("前端API流式请求错误:", error);

      // 添加更详细的错误信息
      if (error.status) console.error(`错误状态码: ${error.status}`);
      if (error.message) console.error(`错误消息: ${error.message}`);
      if (error.code) console.error(`错误代码: ${error.code}`);
      if (error.type) console.error(`错误类型: ${error.type}`);
      if (error.stack) console.error(`堆栈: ${error.stack}`);

      // 检查是否是用户主动中止
      if (error.name === 'AbortError' || (error instanceof DOMException && error.name === 'AbortError')) {
        console.log("前端Stream generation aborted by user.");
        const abortError = new Error('AbortError');
        abortError.name = 'AbortError';
        throw abortError;
      }

      // 如果错误消息已经是纯数字状态码，直接使用
      if (/^\d{3}$/.test(error.message)) {
        throw new Error(error.message);
      }

      const errorMessage = handleAIError(error);
      throw new Error(errorMessage);
    }
  }
};

// 导出简化的API
export const generateAIContent = AIGenerator.generate;
export const generateAIContentStream = AIGenerator.generateStream;
