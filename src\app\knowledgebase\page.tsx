'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';

import TopBar from '@/components/TopBar';
import { Work, Knowledge } from '@/data';
import { getAllWorks, getKnowledgeByWorkId, getAllKnowledge, addKnowledge, updateKnowledge, deleteKnowledge } from '@/data';
import KnowledgeFilters from '@/components/knowledgebase/KnowledgeFilters';
import KnowledgeList from '@/components/knowledgebase/KnowledgeList';
import KnowledgeContent from '@/components/knowledgebase/KnowledgeContent';
import EmptyKnowledgeState from '@/components/knowledgebase/EmptyKnowledgeState';
import DeleteConfirmDialog from '@/components/knowledgebase/DeleteConfirmDialog';

/**
 * 知识库页面 - 新版
 */

export default function KnowledgeBasePage() {
  const router = useRouter();

  // 基础状态
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  // 作品相关状态
  const [works, setWorks] = useState<Work[]>([]);
  const [selectedWorkId, setSelectedWorkId] = useState<number | null>(null);



  // 知识相关状态
  const [knowledgeItems, setKnowledgeItems] = useState<Knowledge[]>([]);
  const [selectedKnowledge, setSelectedKnowledge] = useState<Knowledge | null>(null);
  const [editedKnowledge, setEditedKnowledge] = useState<Knowledge | null>(null);
  const [newTagInput, setNewTagInput] = useState('');

  // 删除确认弹窗状态
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [knowledgeToDelete, setKnowledgeToDelete] = useState<Knowledge | null>(null);

  // 加载作品列表
  useEffect(() => {
    const fetchWorks = async () => {
      try {
        const allWorks = await getAllWorks();
        setWorks(allWorks);

        // 默认选择第一个作品
        if (allWorks.length > 0 && !selectedWorkId) {
          setSelectedWorkId(allWorks[0].id || 0);
        }
      } catch (error) {
        console.error('获取作品失败:', error);
        setError('获取作品列表失败，请稍后再试');
      } finally {
        setIsLoading(false);
      }
    };

    fetchWorks();
  }, []);

  // 当选中的作品变化时，加载该作品的知识
  useEffect(() => {
    const fetchKnowledge = async () => {
      if (!selectedWorkId) return;

      // 加载选中作品的知识
      try {
        const workKnowledge = await getKnowledgeByWorkId(selectedWorkId);
        setKnowledgeItems(workKnowledge);
        // 默认选中第一个知识
        if (workKnowledge.length > 0) {
          setSelectedKnowledge(workKnowledge[0]);
          setEditedKnowledge(workKnowledge[0]);
        } else {
          setSelectedKnowledge(null);
          setEditedKnowledge(null);
        }
      } catch (error) {
        console.error(`获取作品ID为${selectedWorkId}的知识失败:`, error);
        setError('获取知识失败，请稍后再试');
      }
    };

    fetchKnowledge();
  }, [selectedWorkId]);

  // 当选中的知识变化时，更新编辑状态
  useEffect(() => {
    if (selectedKnowledge) {
      setEditedKnowledge(selectedKnowledge);
    } else {
      setEditedKnowledge(null);
    }
  }, [selectedKnowledge]);

  // 处理知识条目编辑
  const handleKnowledgeEdit = (field: keyof Knowledge, value: any) => {
    if (!editedKnowledge) return;

    setEditedKnowledge({
      ...editedKnowledge,
      [field]: value,
      updatedAt: new Date()
    });
  };

  // 自动保存知识条目 - 使用防抖
  useEffect(() => {
    if (!editedKnowledge || !selectedKnowledge || editedKnowledge.id !== selectedKnowledge.id) return;

    const saveTimer = setTimeout(() => {
      handleSaveKnowledge();
    }, 2000); // 2秒后自动保存

    return () => clearTimeout(saveTimer);
  }, [editedKnowledge]);

  // 保存知识
  const handleSaveKnowledge = async () => {
    if (!editedKnowledge || !editedKnowledge.id) return;

    setIsSaving(true);
    try {
      await updateKnowledge(editedKnowledge);

      // 更新知识列表
      if (selectedWorkId) {
        const workKnowledge = await getKnowledgeByWorkId(selectedWorkId);
        setKnowledgeItems(workKnowledge);
      }

      // 更新选中的知识
      setSelectedKnowledge(editedKnowledge);
      setError(null);
    } catch (error) {
      console.error('保存知识失败:', error);
      setError('保存知识失败，请稍后再试');
    } finally {
      setIsSaving(false);
    }
  };

  // 创建新知识
  const handleCreateKnowledge = async () => {
    // 如果没有选择特定作品，不允许创建
    if (!selectedWorkId) return;

    try {
      const newKnowledge: Omit<Knowledge, 'id'> = {
        title: '新知识',
        content: '',
        workId: selectedWorkId, // 使用选中的作品ID
        tags: [],
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const createdKnowledge = await addKnowledge(newKnowledge);

      // 重新加载知识列表
      if (selectedWorkId) {
        const workKnowledge = await getKnowledgeByWorkId(selectedWorkId);
        setKnowledgeItems(workKnowledge);
      }

      // 选中新创建的知识
      setSelectedKnowledge(createdKnowledge);
      setEditedKnowledge(createdKnowledge);
    } catch (error) {
      console.error('创建知识失败:', error);
      setError('创建知识失败，请稍后再试');
    }
  };

  // 删除知识
  const handleDeleteKnowledge = async () => {
    if (!selectedKnowledge || !selectedKnowledge.id) return;

    try {
      await deleteKnowledge(selectedKnowledge.id);

      // 重新加载知识列表
      if (selectedWorkId) {
        const workKnowledge = await getKnowledgeByWorkId(selectedWorkId);
        setKnowledgeItems(workKnowledge);
      }

      // 清除选中的知识
      setSelectedKnowledge(null);
      setEditedKnowledge(null);
      setShowDeleteConfirm(false);
    } catch (error) {
      console.error('删除知识失败:', error);
      setError('删除知识失败，请稍后再试');
    }
  };

  // 添加标签 - 已移动到KnowledgeContent组件，但保留以兼容原有代码
  const handleAddTag = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && newTagInput.trim() && editedKnowledge) {
      const newTags = [...(editedKnowledge.tags || []), newTagInput.trim()];
      handleKnowledgeEdit('tags', newTags);
      setNewTagInput('');
    }
  };

  // 删除标签
  const handleRemoveTag = (tagIndex: number) => {
    if (!editedKnowledge || !editedKnowledge.tags) return;

    const newTags = [...editedKnowledge.tags];
    newTags.splice(tagIndex, 1);
    handleKnowledgeEdit('tags', newTags);
  };

  // 处理编辑标题 - 接收更新后的知识对象
  const handleEditTitle = async (updatedKnowledge: Knowledge) => {
    if (!updatedKnowledge.id) return;

    try {
      console.log("[handleEditTitle] 更新知识标题:", updatedKnowledge.title);

      // 更新标题
      const savedKnowledge = await updateKnowledge(updatedKnowledge);

      // 重新加载知识列表
      if (selectedWorkId) {
        const workKnowledge = await getKnowledgeByWorkId(selectedWorkId);
        setKnowledgeItems(workKnowledge);
      }

      // 如果当前选中的是被编辑的知识，更新选中状态
      if (selectedKnowledge?.id === updatedKnowledge.id) {
        setSelectedKnowledge(savedKnowledge);
        setEditedKnowledge(savedKnowledge);
      }
    } catch (error) {
      console.error('更新知识标题失败:', error);
    }
  };

  // 处理从列表中删除知识
  const handleDeleteFromList = (knowledge: Knowledge) => {
    if (!knowledge.id) return;

    // 显示删除确认弹窗
    setKnowledgeToDelete(knowledge);
    setShowDeleteConfirm(true);
  };

  // 确认删除知识
  const confirmDelete = async () => {
    if (!knowledgeToDelete || !knowledgeToDelete.id) return;

    try {
      await deleteKnowledge(knowledgeToDelete.id);

      // 重新加载知识列表
      if (selectedWorkId) {
        const workKnowledge = await getKnowledgeByWorkId(selectedWorkId);
        setKnowledgeItems(workKnowledge);
      }

      // 如果当前选中的是被删除的知识，清除选中状态
      if (selectedKnowledge?.id === knowledgeToDelete.id) {
        setSelectedKnowledge(null);
        setEditedKnowledge(null);
      }

      // 关闭弹窗
      setShowDeleteConfirm(false);
      setKnowledgeToDelete(null);
    } catch (error) {
      console.error('删除知识失败:', error);
    }
  };

  // 取消删除
  const cancelDelete = () => {
    setShowDeleteConfirm(false);
    setKnowledgeToDelete(null);
  };

  // 不再需要过滤作品列表，因为我们使用下拉菜单选择作品

  // 过滤知识列表
  const filteredKnowledge = knowledgeItems.filter(knowledge =>
    // 搜索过滤
    (searchTerm === '' ||
     knowledge.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
     knowledge.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
     (knowledge.tags && knowledge.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))))
  );

  return (
    <div className="flex h-screen bg-bg-color animate-fadeIn overflow-hidden">
      {/* 背景网格 */}
      <div className="grid-background"></div>

      {/* 装饰元素 */}
      <div className="dot hidden md:block" style={{ top: "120px", left: "15%" }}></div>
      <div className="dot" style={{ bottom: "80px", right: "20%" }}></div>
      <div className="dot hidden md:block" style={{ top: "30%", right: "25%" }}></div>
      <div className="dot hidden md:block" style={{ bottom: "40%", left: "30%" }}></div>

      <svg className="wave hidden md:block" style={{ bottom: "20px", left: "10%" }} width="100" height="20" viewBox="0 0 100 20">
        <path d="M0,10 Q25,0 50,10 T100,10" fill="none" stroke="var(--accent-brown)" strokeWidth="2" />
      </svg>

      {/* 主内容区 */}
      <div className="flex-1 flex flex-col h-screen max-h-screen overflow-hidden">
        {/* 顶部导航栏 */}
        <TopBar
          showBackButton={true}
        />

        {/* 主要内容区 - 使用大卡片包裹整个内容 */}
        <div className="p-4 md:p-6" style={{ maxHeight: "calc(100vh - 80px)", height: "calc(100vh - 80px)" }}>
          <div className="ghibli-card h-full p-0 flex flex-col relative overflow-hidden">
            <div className="tape -rotate-2 -left-2 -top-2" style={{ backgroundColor: 'rgba(125,133,204,0.7)' }}>
              <div className="tape-texture"></div>
            </div>

            {/* 知识库主内容区 - 两栏布局 */}
            <div className="flex-1 flex overflow-hidden">
              {/* 左侧栏 - 知识列表 */}
              <div className="w-96 border-r border-[rgba(125,133,204,0.2)] flex flex-col overflow-hidden bg-white animate-slideIn">
                <KnowledgeFilters
                  works={works}
                  selectedWorkId={selectedWorkId}
                  knowledgeCount={filteredKnowledge.length}
                  searchTerm={searchTerm}
                  onWorkChange={setSelectedWorkId}
                  onSearchChange={setSearchTerm}
                />

                <div className="px-4 py-3 border-b border-[rgba(125,133,204,0.1)] bg-[rgba(125,133,204,0.03)] flex justify-between items-center">
                  <h3 className="text-lg font-medium text-text-dark flex items-center font-ma-shan">
                    <span className="material-icons text-[#7D85CC] mr-2">folder_special</span>
                    知识列表
                  </h3>
                  <div className="flex items-center space-x-2">
                    <button
                      className={`px-3 py-1.5 rounded-full flex items-center shadow-sm text-sm ${
                        !selectedWorkId
                          ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                          : 'bg-[#7D85CC] text-white hover:bg-[#6b73b3] transition-colors duration-200'
                      }`}
                      onClick={handleCreateKnowledge}
                      disabled={!selectedWorkId}
                    >
                      <span className="material-icons text-sm mr-1">add</span>
                      新建知识
                    </button>
                    <div className="relative">
                      <input
                        type="text"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        placeholder="搜索知识..."
                        className="pl-8 pr-3 py-1.5 bg-white border border-[rgba(125,133,204,0.2)] rounded-full text-sm focus:outline-none focus:ring-1 focus:ring-[#7D85CC] w-32"
                      />
                      <span className="material-icons text-text-light text-sm absolute left-2 top-1.5">search</span>
                    </div>
                  </div>
                </div>



                <KnowledgeList
                  knowledgeItems={filteredKnowledge}
                  selectedKnowledge={selectedKnowledge}
                  isLoading={isLoading}
                  selectedWorkId={selectedWorkId}
                  onKnowledgeSelect={setSelectedKnowledge}
                  onCreateKnowledge={handleCreateKnowledge}
                  onEditTitle={handleEditTitle}
                  onDeleteKnowledge={handleDeleteFromList}
                />

              </div>

              {/* 右侧栏 - 知识内容编辑区 */}
              <div className="flex-1 flex flex-col overflow-hidden bg-white animate-fadeIn">
                {editedKnowledge ? (
                  <KnowledgeContent
                    knowledge={editedKnowledge}
                    isSaving={isSaving}
                    onTitleChange={(title) => handleKnowledgeEdit('title', title)}
                    onContentChange={(content) => handleKnowledgeEdit('content', content)}
                    onAddTag={(tag) => {
                      const newTags = [...(editedKnowledge.tags || []), tag];
                      handleKnowledgeEdit('tags', newTags);
                    }}
                    onRemoveTag={handleRemoveTag}
                  />
                ) : (
                  <EmptyKnowledgeState
                    selectedWorkId={selectedWorkId}
                    onCreateKnowledge={handleCreateKnowledge}
                  />
                )}
              </div>
            </div>
            <div className="page-curl"></div>
          </div>
        </div>
      </div>

      {/* 删除确认弹窗 */}
      {showDeleteConfirm && knowledgeToDelete && (
        <DeleteConfirmDialog
          title={knowledgeToDelete.title}
          onCancel={cancelDelete}
          onConfirm={confirmDelete}
        />
      )}
    </div>
  );
}
