/**
 * 后端计费服务模块
 * 只能在API目录中使用，包含完整的扣费功能
 * ⚠️ 此文件不应该被前端组件导入
 */
import { createClient } from '@supabase/supabase-js';

// 创建Supabase客户端（使用anon key，但函数有DEFINER权限）
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

// 会员信息接口
export interface MembershipInfo {
  is_active: boolean;
  level: string;
  days_remaining: number | null;
  word_limit: number | null;
  word_used: number;
  word_remaining: number | null;
}

// 计费结果接口
export interface BillingResult {
  success: boolean;
  message?: string;
  remainingBalance?: number;
}

/**
 * 后端计费服务类
 * 包含完整的计费功能，只能在后端API中使用
 */
export class BackendBillingService {
  /**
   * 检查用户会员状态（使用直接表查询）
   * @param userUuid 用户UUID
   * @returns 会员信息
   */
  static async getMembershipInfo(userUuid: string): Promise<MembershipInfo | null> {
    try {
      // 查询membership-look表，统一使用look表进行查询
      const { data, error } = await supabase
        .from('membership-look')
        .select('*')
        .eq('user_id', userUuid)
        .single();

      if (error) {
        console.error('获取会员信息失败:', error);
        return null;
      }

      if (!data) {
        return null;
      }

      // 转换为标准格式
      const wordRemaining = data.word_count_limit ?
        Math.max(0, data.word_count_limit - (data.word_count_used || 0)) : null;

      return {
        is_active: data.membership_level !== '免费',
        level: data.membership_level,
        days_remaining: null, // 暂不支持天数计算
        word_limit: data.word_count_limit,
        word_used: data.word_count_used || 0,
        word_remaining: wordRemaining
      } as MembershipInfo;
    } catch (error) {
      console.error('获取会员信息异常:', error);
      return null;
    }
  }

  /**
   * 检查用户字数余额（使用认证客户端查询）
   * @param userUuid 用户UUID
   * @param accessToken 用户访问令牌（可选，如果提供则使用认证客户端）
   * @returns 余额数量，-1表示用户不存在，-2表示无限制，>=0表示剩余字数
   */
  static async checkBalance(userUuid: string, accessToken?: string): Promise<number> {
    try {
      // 根据是否有访问令牌选择客户端
      let clientToUse = supabase;
      if (accessToken) {
        clientToUse = this.createAuthenticatedClient(accessToken);
      }

      // 查询membership-look表，使用认证客户端进行查询
      const { data, error } = await clientToUse
        .from('membership-look')
        .select('word_count_used, word_count_limit, membership_level')
        .eq('user_id', userUuid)
        .single();

      if (error) {
        console.error('检查余额失败:', error);
        return -1;
      }

      if (!data) {
        return -1;
      }

      // 删除免费机制：所有用户都需要付费使用

      // 计算剩余余额
      const used = data.word_count_used || 0;
      const limit = data.word_count_limit || 0;
      return Math.max(0, limit - used);
    } catch (error) {
      console.error('检查余额异常:', error);
      return -1;
    }
  }

  /**
   * ⚠️ 已弃用：扣除用户字数功能
   * 此方法已被专用计费API取代，不应再直接调用
   * 请使用 /api/billing/deduct 端点进行扣费操作
   * @deprecated 请使用专用计费API端点
   * @param userUuid 用户UUID
   * @param amount 扣除数量
   * @param userAccessToken 用户的访问令牌
   * @returns 扣费结果
   */
  static async deductWordCount(userUuid: string, amount: number, userAccessToken?: string): Promise<BillingResult> {
    console.warn('⚠️ BackendBillingService.deductWordCount 已弃用，请使用专用计费API端点');
    console.warn('建议使用: POST /api/billing/deduct');

    // 为了向后兼容，保留原有逻辑，但添加警告
    try {
      // 先检查余额
      const balance = await this.checkBalance(userUuid);

      if (balance === -1) {
        return {
          success: false,
          message: '用户不存在'
        };
      }

      // 删除免费机制：所有用户都需要付费使用

      // 检查余额是否足够
      if (balance < amount) {
        return {
          success: false,
          message: `字数余额不足，当前余额：${balance}字，需要：${amount}字`,
          remainingBalance: balance
        };
      }

      // 执行扣费 - 使用认证客户端
      let clientToUse = supabase;
      if (userAccessToken) {
        clientToUse = this.createAuthenticatedClient(userAccessToken);
      }

      const { data, error } = await clientToUse
        .rpc('deduct_user_word_count', {
          user_uuid: userUuid,
          deduct_amount: amount
        });

      if (error || !data) {
        return {
          success: false,
          message: `扣费失败: ${error?.message || '未知错误'}`,
          remainingBalance: balance
        };
      }

      return {
        success: true,
        message: '扣费成功',
        remainingBalance: balance - amount
      };
    } catch (error) {
      console.error('扣费异常:', error);
      return {
        success: false,
        message: '扣费异常'
      };
    }
  }

  /**
   * 创建带用户认证的Supabase客户端
   * @param accessToken 用户访问令牌
   * @returns 认证后的Supabase客户端
   */
  static createAuthenticatedClient(accessToken: string) {
    const authenticatedSupabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        global: {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        }
      }
    );

    return authenticatedSupabase;
  }

  // 模型计费倍率配置表
  private static MODEL_BILLING_RATES = {
    'ceshi': { // 测试模型
      inputRate: 5,   // 输入倍率
      outputRate: 40  // 输出倍率
    },
    'kelaode': { // 克劳德模型
      inputRate: 10,  // 输入倍率
      outputRate: 55  // 输出倍率
    }
  };

  /**
   * 计算Token消耗的字数（新倍率计算方式）
   * @param promptTokens 输入token数
   * @param totalTokens 总token数
   * @param modelCode 模型代号
   * @returns 总消耗字数
   */
  static calculateTokenCost(promptTokens: number, totalTokens: number, modelCode: string): number {
    // 输出token = 总token - 输入token
    const outputTokens = totalTokens - promptTokens;

    // 获取模型倍率配置，如果模型不存在则使用测试模型的倍率
    const rates = this.MODEL_BILLING_RATES[modelCode as keyof typeof this.MODEL_BILLING_RATES] || this.MODEL_BILLING_RATES['ceshi'];

    // 修正后的倍率计算：输入token * 输入倍率 + 输出token * 输出倍率
    const inputCost = promptTokens * rates.inputRate;
    const outputCost = outputTokens * rates.outputRate;
    return inputCost + outputCost;
  }

  /**
   * 检查用户是否可以使用AI功能（支持认证用户免费额度）
   * @param userUuid 用户UUID
   * @param estimatedCost 预估消耗（可选）
   * @param accessToken 用户访问令牌（可选，如果提供则使用认证客户端）
   * @param modelCode 模型代号（可选，用于检查模型权限）
   * @returns 是否可以使用
   */
  static async canUseAI(userUuid: string, estimatedCost: number = 0, accessToken?: string, modelCode?: string): Promise<{
    canUse: boolean;
    message?: string;
    balance?: number;
    usesFreeQuota?: boolean;
  }> {
    try {
      // 根据是否有访问令牌选择客户端
      let clientToUse = supabase;
      if (accessToken) {
        clientToUse = this.createAuthenticatedClient(accessToken);
      }

      // 查询用户认证状态和余额信息
      const { data, error } = await clientToUse
        .from('membership-look')
        .select('word_count_used, word_count_limit, membership_level, is_verified, daily_free_quota, daily_free_used, last_free_reset_date')
        .eq('user_id', userUuid)
        .single();

      if (error || !data) {
        return { canUse: false, message: '用户信息获取失败' };
      }

      // 检查是否为非认证用户
      if (!data.is_verified) {
        return { canUse: false, message: '非认证用户禁止使用AI功能，请先进行用户认证' };
      }

      // 认证用户：检查是否可以使用免费额度（仅限ceshi模型）
      if (modelCode === 'ceshi') {
        // 检查是否需要重置每日免费额度
        const today = new Date().toISOString().split('T')[0];
        const lastReset = data.last_free_reset_date;

        let availableFreeQuota = data.daily_free_quota - data.daily_free_used;

        // 如果日期不是今天，重置免费额度
        if (lastReset !== today) {
          availableFreeQuota = data.daily_free_quota;
        }

        // 如果有免费额度，可以使用
        if (availableFreeQuota > 0) {
          return {
            canUse: true,
            balance: availableFreeQuota,
            usesFreeQuota: true,
            message: `使用免费额度，剩余${availableFreeQuota}次`
          };
        }
      }

      // 免费额度用完或非ceshi模型，检查付费余额
      const used = data.word_count_used || 0;
      const limit = data.word_count_limit || 0;
      const balance = Math.max(0, limit - used);

      if (balance < estimatedCost) {
        return {
          canUse: false,
          message: `字数余额不足，当前余额：${balance}字`,
          balance: balance
        };
      }

      return {
        canUse: true,
        balance: balance,
        usesFreeQuota: false
      };
    } catch (error) {
      console.error('检查AI使用权限异常:', error);
      return {
        canUse: false,
        message: '检查权限失败'
      };
    }
  }

  /**
   * ⚠️ 已删除免费模型机制
   * 所有模型现在都需要付费使用
   * @deprecated 免费模型机制已删除
   */
  static isFreeModel(modelCode: string): boolean {
    return false; // 所有模型都需要付费
  }

  /**
   * 创建免费用户记录
   * @param userUuid 用户UUID
   * @param userEmail 用户邮箱（可选，用于记录）
   * @returns 是否创建成功
   */
  static async createFreeUser(userUuid: string, userEmail?: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('membership-look')
        .insert({
          user_id: userUuid,
          email: userEmail || null,
          membership_level: '免费',
          word_count_used: 0
        });

      return !error;
    } catch (error) {
      console.error('创建免费用户失败:', error);
      return false;
    }
  }
}

// 导出默认实例
export default BackendBillingService;
