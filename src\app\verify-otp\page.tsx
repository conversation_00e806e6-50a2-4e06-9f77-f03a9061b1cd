'use client';

import React, { useState, useEffect, useRef, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import { verifyEmailOtp, resendOtp } from '@/services/userService';

/**
 * OTP验证页面内容组件
 */
function VerifyOtpContent() {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const email = searchParams.get('email') || '';
  
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [countdown, setCountdown] = useState(0);
  
  // 输入框引用
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  // 如果已登录，跳转到首页
  useEffect(() => {
    if (isAuthenticated && !isLoading) {
      router.push('/');
    }
  }, [isAuthenticated, isLoading, router]);

  // 如果没有邮箱参数，跳转到注册页面
  useEffect(() => {
    if (!email) {
      router.push('/register');
    }
  }, [email, router]);

  // 倒计时逻辑
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  // 处理OTP输入
  const handleOtpChange = (index: number, value: string) => {
    // 只允许数字
    if (!/^\d*$/.test(value)) return;

    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    // 自动聚焦到下一个输入框
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }

    // 清除错误信息
    if (error) setError(null);
  };

  // 处理键盘事件
  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      // 如果当前输入框为空且按下退格键，聚焦到前一个输入框
      inputRefs.current[index - 1]?.focus();
    } else if (e.key === 'Enter') {
      // 按下回车键提交
      handleSubmit();
    }
  };

  // 处理粘贴事件
  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text').replace(/\D/g, '').slice(0, 6);
    
    if (pastedData.length === 6) {
      const newOtp = pastedData.split('');
      setOtp(newOtp);
      inputRefs.current[5]?.focus();
    }
  };

  // 提交验证
  const handleSubmit = async () => {
    const otpCode = otp.join('');
    
    if (otpCode.length !== 6) {
      setError('请输入完整的6位验证码');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const result = await verifyEmailOtp(email, otpCode);
      
      if (result.user) {
        setSuccess('验证成功！正在跳转...');
        
        // 延迟跳转，让用户看到成功消息
        setTimeout(() => {
          router.push('/');
        }, 1500);
      }
    } catch (error) {
      console.error('OTP验证失败:', error);
      setError(error instanceof Error ? error.message : '验证失败，请重试');
      
      // 清空输入框并聚焦到第一个
      setOtp(['', '', '', '', '', '']);
      inputRefs.current[0]?.focus();
    } finally {
      setIsSubmitting(false);
    }
  };

  // 重新发送验证码
  const handleResend = async () => {
    if (countdown > 0 || isResending) return;

    setIsResending(true);
    setError(null);

    try {
      await resendOtp(email);
      setSuccess('验证码已重新发送，请查收邮件');
      setCountdown(60); // 60秒倒计时
      
      // 清空输入框
      setOtp(['', '', '', '', '', '']);
      inputRefs.current[0]?.focus();
    } catch (error) {
      console.error('重新发送失败:', error);
      setError(error instanceof Error ? error.message : '重新发送失败，请重试');
    } finally {
      setIsResending(false);
    }
  };

  // 如果正在加载或已登录，显示加载中
  if (isLoading || isAuthenticated) {
    return (
      <div className="min-h-screen bg-bg-color flex flex-col">
        <div className="flex-1 flex items-center justify-center">
          <div className="animate-pulse text-primary-green">
            <svg className="animate-spin h-10 w-10 text-primary-green" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-bg-color flex items-center justify-center">
      <div className="w-full max-w-md">
        <div className="ghibli-card p-8 relative">
          <h2 className="text-2xl font-bold text-center mb-6 text-text-dark">
            验证邮箱
          </h2>

          <div className="text-center mb-6">
            <p className="text-text-medium text-sm mb-2">
              我们已向以下邮箱发送了6位验证码：
            </p>
            <p className="text-primary-green font-medium text-sm break-all">
              {email}
            </p>
          </div>

          {error && (
            <div className="bg-red-50 text-red-600 p-3 rounded-lg mb-4 text-sm">
              {error}
            </div>
          )}

          {success && (
            <div className="bg-green-50 text-green-600 p-3 rounded-lg mb-4 text-sm">
              {success}
            </div>
          )}

          {/* OTP输入框 */}
          <div className="flex justify-center space-x-3 mb-6">
            {otp.map((digit, index) => (
              <input
                key={index}
                ref={(el) => (inputRefs.current[index] = el)}
                type="text"
                maxLength={1}
                value={digit}
                onChange={(e) => handleOtpChange(index, e.target.value)}
                onKeyDown={(e) => handleKeyDown(index, e)}
                onPaste={index === 0 ? handlePaste : undefined}
                className="w-12 h-12 text-center text-xl font-bold bg-white bg-opacity-70 border border-[rgba(120,180,140,0.3)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[rgba(120,180,140,0.5)] transition-all duration-200 text-text-dark"
                disabled={isSubmitting}
              />
            ))}
          </div>

          {/* 提交按钮 */}
          <button
            onClick={handleSubmit}
            className="w-full bg-primary-green text-white py-2 px-4 rounded-full hover:bg-[#4a8d5b] transition-colors duration-200 flex items-center justify-center mb-4"
            disabled={isSubmitting || otp.join('').length !== 6}
          >
            {isSubmitting ? (
              <span className="flex items-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                验证中...
              </span>
            ) : '验证'}
          </button>

          {/* 重新发送和返回按钮 */}
          <div className="flex justify-between items-center text-sm">
            <button
              onClick={handleResend}
              className={`text-primary-green hover:underline ${
                countdown > 0 || isResending ? 'opacity-50 cursor-not-allowed' : ''
              }`}
              disabled={countdown > 0 || isResending}
            >
              {isResending ? '发送中...' : countdown > 0 ? `重新发送 (${countdown}s)` : '重新发送验证码'}
            </button>
            
            <button
              onClick={() => router.push('/register')}
              className="text-text-medium hover:text-text-dark hover:underline"
            >
              返回注册
            </button>
          </div>

          <div className="page-curl"></div>
        </div>
      </div>
    </div>
  );
}

/**
 * OTP验证页面（带Suspense包装）
 */
export default function VerifyOtpPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-bg-color flex flex-col">
        <div className="flex-1 flex items-center justify-center">
          <div className="animate-pulse text-primary-green">
            <svg className="animate-spin h-10 w-10 text-primary-green" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </div>
        </div>
      </div>
    }>
      <VerifyOtpContent />
    </Suspense>
  );
}
