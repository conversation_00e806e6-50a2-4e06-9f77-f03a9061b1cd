# Bug修复与功能增强综合计划

**目标**: 1. 修复 `page.tsx` 中新出现的 TypeScript 编译错误。 2. 完成 AI 覆盖层编辑器的功能扩展。

---

### **第一部分：修复 `page.tsx` 中的编译错误**

**文件**: `src/app/works/[id]/page.tsx`

1.  **修复 `EventTarget` 类型错误**:
    *   **定位**: 第 252 行 `onChange(e.target.value);`
    *   **方案**: 将 `onChange` 事件处理函数改为 `onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => onChange(e.target.value)}`，为事件对象 `e` 提供明确的类型。

2.  **修复 `Character` 类型不匹配错误**:
    *   **定位**: 第 501 行 `setCharacters(updatedCharacters);`
    *   **方案**: 确保 `gender` 字段符合 `CharacterGender` 类型。在更新角色时，需要对 `gender` 字符串进行类型断言，例如 `gender: gender as CharacterGender`。

3.  **修复 `recreateMainDatabase` 缺失错误**:
    *   **定位**: 第 1040 行 `const { recreateMainDatabase } = await import('@/data');`
    *   **方案**: 这是一个开发初期用的函数，现在已经不再需要。直接删除从 1037 行到 1048 行的整个 `try...catch` 块。

4.  **修复 `<RichTextEditor />` props 缺失错误**:
    *   **定位**: 第 1514 行 `<RichTextEditor ... />` 调用处。
    *   **方案**: 将 `showAIOverlay`, `setShowAIOverlay`, `selectedText`, `setSelectedText` 这几个状态从 `RichTextEditor` 组件内部移到 `WorkDetailPage` 组件中，然后将它们作为 props 传递给 `RichTextEditor`。

---

### **第二部分：执行 AI 覆盖层功能扩展**

此部分与 `feature-ai-overlay-editor-plan-final.md` 中的计划完全相同。

#### **1. 升级 `AIOverlayPanel.tsx` 组件**

*   **文件**: `src/components/works/AIOverlayPanel.tsx`
*   **操作**:
    *   扩展 `AIOverlayPanelProps` 接口。
    *   实现状态同步与内容切换逻辑。
    *   连接编辑器 `value` 和 `onChange`。

#### **2. 升级 `page.tsx` 组件**

*   **文件**: `src/app/works/[id]/page.tsx`
*   **操作**:
    *   在调用 `<AIOverlayPanel />` 的地方传递所有新的 props。

---

**执行建议**:

请将此综合计划交给一个拥有代码修改权限的角色执行。建议先完成 **第一部分** 的 Bug 修复，再执行 **第二部分** 的功能增强。