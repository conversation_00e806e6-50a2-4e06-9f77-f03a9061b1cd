/**
 * 提示词删除确认模态窗口组件
 */
import React, { useState } from 'react';
import { Modal } from '@/components/common/modals';

// 用户提示词接口
interface UserPrompt {
  id: string;
  title: string;
  description?: string;
  type: string;
  content?: string;
  created_at: string;
  updated_at: string;
  created_by?: string;
}

interface PromptDeleteModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  prompt: UserPrompt | null;
}

export default function PromptDeleteModal({
  isOpen,
  onClose,
  onConfirm,
  prompt
}: PromptDeleteModalProps) {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleConfirm = async () => {
    setIsDeleting(true);
    try {
      await onConfirm();
    } finally {
      setIsDeleting(false);
    }
  };

  if (!prompt) return null;

  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center animate-fadeIn" role="dialog" aria-modal="true">
      {/* 背景遮罩 */}
      <div
        className="fixed inset-0 bg-black/60 backdrop-blur-sm transition-all duration-300"
        onClick={onClose}
      ></div>

      {/* 删除确认窗口 */}
      <div className="relative bg-white rounded-2xl shadow-2xl transform transition-all duration-300 scale-100 w-full max-w-md mx-4 overflow-hidden">

        {/* 危险装饰条 */}
        <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-red-500 via-red-600 to-red-500"></div>

        {/* 背景装饰 */}
        <div className="absolute inset-0 bg-gradient-to-br from-red-50/30 to-orange-50/20 opacity-60"></div>
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-red-100/40 to-transparent rounded-full transform translate-x-16 -translate-y-16"></div>

        <div className="relative z-10 p-6">
          {/* 头部区域 */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center">
              <div className="relative w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center shadow-lg mr-3">
                <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-full"></div>
                <span className="material-icons text-white text-xl animate-pulse">warning</span>
                <div className="absolute inset-0 bg-gradient-to-br from-transparent to-black/10 rounded-full"></div>
              </div>
              <h3 className="text-xl font-bold text-gray-800 font-ma-shan">
                删除提示词
              </h3>
            </div>
            <button
              onClick={onClose}
              disabled={isDeleting}
              className="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-all duration-200 transform hover:scale-110 disabled:opacity-50"
            >
              <span className="material-icons text-lg">close</span>
            </button>
          </div>

          {/* 警告内容区域 */}
          <div className="mb-6">
            <div className="flex items-start space-x-3 mb-4">
              <div className="w-6 h-6 bg-gradient-to-br from-orange-400 to-orange-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="material-icons text-white text-sm">priority_high</span>
              </div>
              <div className="flex-1">
                <h4 className="text-lg font-semibold text-gray-800 mb-2">
                  确认删除提示词
                </h4>
                <p className="text-gray-600 leading-relaxed">
                  您确定要删除提示词
                  <span className="font-semibold text-gray-800 mx-1 px-2 py-0.5 bg-gray-100 rounded">
                    "{prompt.title}"
                  </span>
                  吗？
                </p>
              </div>
            </div>

            {/* 危险警告框 */}
            <div className="relative p-4 bg-gradient-to-r from-red-50 to-red-100/80 border-l-4 border-red-500 rounded-r-lg shadow-sm">
              <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-bl from-red-200/30 to-transparent rounded-full transform translate-x-8 -translate-y-8"></div>
              <div className="relative z-10 flex items-start">
                <div className="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0 mr-3 mt-0.5">
                  <span className="material-icons text-white text-xs">info</span>
                </div>
                <div>
                  <p className="text-red-800 text-sm font-medium mb-1">
                    此操作无法撤销
                  </p>
                  <p className="text-red-700 text-sm leading-relaxed">
                    删除后将永久丢失该提示词的所有内容，包括标题、描述和提示词内容。
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* 按钮组 */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200/60">
            <button
              type="button"
              onClick={onClose}
              disabled={isDeleting}
              className="px-6 py-2.5 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-xl transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105"
            >
              取消
            </button>
            <button
              type="button"
              onClick={handleConfirm}
              disabled={isDeleting}
              className="group relative px-6 py-2.5 bg-gradient-to-r from-red-600 to-red-700 text-white rounded-xl hover:from-red-700 hover:to-red-800 transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center transform hover:scale-105 shadow-lg hover:shadow-xl"
            >
              <div className="absolute inset-0 bg-white/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
              <div className="relative z-10 flex items-center">
                {isDeleting && (
                  <span className="material-icons animate-spin text-sm mr-2">refresh</span>
                )}
                {isDeleting ? '删除中...' : '确认删除'}
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
