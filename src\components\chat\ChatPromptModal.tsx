'use client';

import React, { useState, useEffect } from 'react';
import { Modal } from '@/components/common/modals';

interface ChatPromptModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (prompt: string) => void;
  initialPrompt?: string;
}

/**
 * AI对话提示词设置模态窗口组件
 */
export const ChatPromptModal: React.FC<ChatPromptModalProps> = ({
  isOpen,
  onClose,
  onSave,
  initialPrompt = ''
}) => {
  const [promptContent, setPromptContent] = useState(initialPrompt);

  // 当模态窗口打开时，重置内容
  useEffect(() => {
    if (isOpen) {
      setPromptContent(initialPrompt);
    }
  }, [isOpen, initialPrompt]);

  // 处理保存
  const handleSave = () => {
    onSave(promptContent);
    onClose();
  };

  // 处理取消
  const handleCancel = () => {
    setPromptContent(initialPrompt); // 恢复原始内容
    onClose();
  };

  // 清空提示词
  const handleClear = () => {
    setPromptContent('');
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleCancel}
      title="提示词设置"
      size="lg"
    >
      <div className="flex flex-col h-full">
        {/* 内容区域 - 可滚动 */}
        <div className="flex-1 overflow-y-auto space-y-4 px-1 pb-2">
          {/* 说明文字 */}
          <div className="text-sm text-gray-600">
            <p>设置系统提示词来定制AI的行为和回复风格。留空则使用默认设置。</p>
          </div>

          {/* 提示词输入框 */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              系统提示词
            </label>
            <textarea
              value={promptContent}
              onChange={(e) => setPromptContent(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#00C250] focus:border-transparent resize-none"
              rows={6}
              placeholder="请输入系统提示词，例如：你是一个专业的写作助手，请用简洁明了的语言回答问题..."
            />
            <div className="text-xs text-gray-500">
              字符数：{promptContent.length}
            </div>
          </div>


        </div>

        {/* 操作按钮 - 固定在底部 */}
        <div className="flex justify-between pt-4 border-t border-gray-200 mt-4 flex-shrink-0">
          <button
            onClick={handleClear}
            className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
          >
            清空
          </button>
          <div className="flex space-x-3">
            <button
              onClick={handleSave}
              className="px-4 py-2 text-sm bg-[#00C250] text-white rounded-lg hover:bg-[#00a843] transition-colors"
            >
              保存
            </button>
            <button
              onClick={handleCancel}
              className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
            >
              取消
            </button>
          </div>
        </div>
      </div>
    </Modal>
  );
};
