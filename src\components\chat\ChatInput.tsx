'use client';

import React, { useState, useRef, useEffect } from 'react';

interface ChatInputProps {
  onSendMessage: (message: string) => void;
  disabled?: boolean;
  placeholder?: string;
}

export const ChatInput: React.FC<ChatInputProps> = ({
  onSendMessage,
  disabled = false,
  placeholder = '输入消息...'
}) => {
  const [message, setMessage] = useState('');
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // 自动调整文本框高度
  const adjustTextareaHeight = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
    }
  };

  useEffect(() => {
    adjustTextareaHeight();
  }, [message]);

  // 发送消息
  const handleSend = () => {
    if (message.trim() && !disabled) {
      onSendMessage(message.trim());
      setMessage('');
    }
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <div className="p-4 bg-white border-t border-gray-100">
      <div className="flex items-end space-x-3">
        {/* 输入框容器 */}
        <div className="flex-1 relative">
          <div className="relative bg-gray-50 rounded-2xl border border-gray-200 focus-within:border-[#5a9d6b] focus-within:bg-white focus-within:chat-input-focus transition-all duration-200 shadow-sm">
            <textarea
              ref={textareaRef}
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={placeholder}
              disabled={disabled}
              rows={1}
              className="w-full px-4 py-3 bg-transparent rounded-2xl resize-none focus:outline-none disabled:cursor-not-allowed text-gray-800 placeholder-gray-500"
              style={{ minHeight: '44px', maxHeight: '100px' }}
            />

            {/* 字符计数 */}
            {message.length > 0 && (
              <div className="absolute bottom-2 right-4 text-xs text-gray-400 bg-white px-2 py-1 rounded-full">
                {message.length}
              </div>
            )}
          </div>
        </div>

        {/* 发送按钮 */}
        <button
          onClick={handleSend}
          disabled={disabled || !message.trim()}
          className="flex-shrink-0 w-12 h-12 bg-gradient-to-r from-[#5a9d6b] to-[#65ad79] text-white rounded-2xl flex items-center justify-center hover:shadow-lg transform hover:scale-105 transition-all duration-200 disabled:bg-gray-300 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-none"
        >
          {disabled ? (
            <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
          ) : (
            <span className="material-icons text-xl">send</span>
          )}
        </button>
      </div>

      {/* 提示文本 */}
      <div className="mt-3 text-xs text-gray-400 text-center flex items-center justify-center space-x-4">
        <span className="flex items-center">
          <kbd className="px-2 py-1 bg-gray-100 rounded-full text-xs mr-1">Enter</kbd>
          发送
        </span>
        <span className="flex items-center">
          <kbd className="px-2 py-1 bg-gray-100 rounded-full text-xs mr-1">Shift</kbd>
          +
          <kbd className="px-2 py-1 bg-gray-100 rounded-full text-xs mx-1">Enter</kbd>
          换行
        </span>
      </div>
    </div>
  );
};
