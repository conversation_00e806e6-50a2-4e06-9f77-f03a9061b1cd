'use client';

import { useRouter } from 'next/navigation';

/**
 * 认证回调页面
 * 提示用户使用新的OTP验证方式
 */
export default function AuthCallbackPage() {
  const router = useRouter();

  return (
    <div className="min-h-screen bg-bg-color flex items-center justify-center">
      <div className="w-full max-w-md">
        <div className="ghibli-card p-8 relative">
          <h2 className="text-2xl font-bold text-center mb-6 text-text-dark">
            验证方式已更新
          </h2>

          <div className="bg-amber-50 text-amber-700 p-4 rounded-lg mb-6 text-sm">
            <div className="flex items-start">
              <svg className="w-5 h-5 text-amber-500 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <div>
                <p className="font-medium mb-2">邮箱链接验证已停用</p>
                <p className="text-sm">
                  我们已升级为更安全的验证码验证方式。请使用邮件中的6位数字验证码进行验证。
                </p>
              </div>
            </div>
          </div>

          <div className="space-y-3 mb-6">
            <div className="flex items-center text-sm text-text-medium">
              <span className="w-6 h-6 bg-primary-green text-white rounded-full flex items-center justify-center text-xs font-bold mr-3">1</span>
              注册账号后，查收验证邮件
            </div>
            <div className="flex items-center text-sm text-text-medium">
              <span className="w-6 h-6 bg-primary-green text-white rounded-full flex items-center justify-center text-xs font-bold mr-3">2</span>
              在验证页面输入邮件中的6位数字验证码
            </div>
            <div className="flex items-center text-sm text-text-medium">
              <span className="w-6 h-6 bg-primary-green text-white rounded-full flex items-center justify-center text-xs font-bold mr-3">3</span>
              验证成功后自动登录
            </div>
          </div>

          <div className="flex space-x-3">
            <button
              onClick={() => router.push('/register')}
              className="flex-1 bg-primary-green text-white py-2 px-4 rounded-full hover:bg-[#4a8d5b] transition-colors duration-200"
            >
              去注册
            </button>
            <button
              onClick={() => router.push('/login')}
              className="flex-1 border border-primary-green text-primary-green py-2 px-4 rounded-full hover:bg-primary-green hover:text-white transition-colors duration-200"
            >
              去登录
            </button>
          </div>

          <div className="page-curl"></div>
        </div>
      </div>
    </div>
  );
}
