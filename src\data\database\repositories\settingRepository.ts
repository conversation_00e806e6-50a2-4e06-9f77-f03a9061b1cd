/**
 * 设定仓库
 */
import { Setting } from '@/types/setting';
import { dbOperations } from '../core/operations';
import { DB_CONFIG } from '../config';

const { MAIN } = DB_CONFIG.NAMES;
const { SETTINGS } = DB_CONFIG.STORES.MAIN;

/**
 * 添加设定
 * @param setting 设定数据
 * @returns 添加后的设定
 */
export const addSetting = async (setting: Setting): Promise<Setting> => {
  console.log('添加设定到数据库:', setting);
  return dbOperations.add<Setting>(MAIN, SETTINGS, setting);
};

/**
 * 获取所有设定
 * @returns 所有设定
 */
export const getAllSettings = async (): Promise<Setting[]> => {
  return dbOperations.getAll<Setting>(MAIN, SETTINGS);
};

/**
 * 根据作品ID获取设定
 * @param workId 作品ID
 * @returns 该作品的所有设定
 */
export const getSettingsByWorkId = async (workId: number): Promise<Setting[]> => {
  const allSettings = await getAllSettings();
  return allSettings.filter(setting => setting.workId === workId)
    .sort((a, b) => a.order - b.order);
};

/**
 * 根据ID获取设定
 * @param id 设定ID
 * @returns 设定
 */
export const getSettingById = async (id: string): Promise<Setting | null> => {
  try {
    return await dbOperations.get<Setting>(MAIN, SETTINGS, id);
  } catch (error) {
    console.error('获取设定失败:', error);
    return null;
  }
};

/**
 * 更新设定
 * @param setting 设定数据
 * @returns 更新后的设定
 */
export const updateSetting = async (setting: Setting): Promise<Setting> => {
  if (!setting.id) throw new Error('Setting ID is required');
  return dbOperations.update<Setting>(MAIN, SETTINGS, setting);
};

/**
 * 删除设定
 * @param id 设定ID
 */
export const deleteSetting = async (id: string): Promise<void> => {
  return dbOperations.remove(MAIN, SETTINGS, id);
};

/**
 * 根据分类获取设定
 * @param workId 作品ID
 * @param category 分类
 * @returns 该分类的所有设定
 */
export const getSettingsByCategory = async (workId: number, category: string): Promise<Setting[]> => {
  const workSettings = await getSettingsByWorkId(workId);
  return workSettings.filter(setting => setting.category === category);
};
