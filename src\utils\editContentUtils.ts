import { Chapter, Character, Outline, Setting, Knowledge } from '@/types';

export type EditMode = 'chapter' | 'character' | 'outline' | 'setting' | 'knowledge';

export interface EditContentResult {
  title: string;
  content: string;
  data: any;
}

export interface EditContentParams {
  editMode: EditMode;
  chapters: Chapter[];
  activeChapter: number;
  characters: Character[];
  activeCharacterId: string | null;
  outlines: Outline[];
  activeOutlineId: string | null;
  settings: Setting[];
  activeSettingId: string | null;
  knowledges: Knowledge[];
  activeKnowledgeId: number | null;
}

/**
 * 根据当前编辑模式获取对应的内容和标题
 */
export const getCurrentEditContent = (params: EditContentParams): EditContentResult => {
  const {
    editMode,
    chapters,
    activeChapter,
    characters,
    activeCharacterId,
    outlines,
    activeOutlineId,
    settings,
    activeSettingId,
    knowledges,
    activeKnowledgeId
  } = params;

  switch (editMode) {
    case 'outline':
      const selectedOutline = outlines.find(outline => outline.id === activeOutlineId);
      return {
        title: selectedOutline?.title || '',
        content: selectedOutline?.content || '',
        data: selectedOutline
      };
    case 'setting':
      const selectedSetting = settings.find(setting => setting.id === activeSettingId);
      return {
        title: selectedSetting?.title || '',
        content: selectedSetting?.content || '',
        data: selectedSetting
      };
    case 'character':
      const selectedCharacter = characters.find(char => char.id === activeCharacterId);
      // 将角色的多个字段合并为content
      const characterContent = selectedCharacter ?
        `性别：${selectedCharacter.gender}\n\n性格：\n${selectedCharacter.personality}\n\n背景：\n${selectedCharacter.background}` : '';
      return {
        title: selectedCharacter?.name || '',
        content: characterContent,
        data: selectedCharacter
      };
    case 'knowledge':
      const selectedKnowledge = knowledges.find(knowledge => knowledge.id === activeKnowledgeId);
      return {
        title: selectedKnowledge?.title || '',
        content: selectedKnowledge?.content || '',
        data: selectedKnowledge
      };
    default: // chapter
      return {
        title: chapters[activeChapter]?.title || '',
        content: chapters[activeChapter]?.content || '',
        data: chapters[activeChapter]
      };
  }
};
