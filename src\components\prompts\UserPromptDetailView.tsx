'use client';

import React from 'react';

// 提示词类型映射
const promptTypeMap = {
  'ai_writing': { label: 'AI写作', color: 'bg-[#5a9d6b] text-white', icon: 'create' },
  'ai_polishing': { label: 'AI润色', color: 'bg-[#7D85CC] text-white', icon: 'auto_fix_high' },
  'worldbuilding': { label: '世界观', color: 'bg-[#E06F9C] text-white', icon: 'public' },
  'character': { label: '角色', color: 'bg-[#9C6FE0] text-white', icon: 'person' },
  'plot': { label: '情节', color: 'bg-[#6F9CE0] text-white', icon: 'timeline' },
  'introduction': { label: '导语', color: 'bg-[#7D85CC] text-white', icon: 'format_quote' },
  'outline': { label: '大纲', color: 'bg-[#E0976F] text-white', icon: 'format_list_bulleted' },
  'detailed_outline': { label: '细纲', color: 'bg-[#E0C56F] text-white', icon: 'subject' }
} as const;

// 将类型颜色转换为胶带颜色
const getTypeColor = (type: string): string => {
  const colorClass = promptTypeMap[type as keyof typeof promptTypeMap]?.color.split(' ')[1] || 'text-[#7D85CC]';
  // 从 text-[#7D85CC] 提取 #7D85CC
  const colorHex = colorClass.match(/#[0-9A-Fa-f]{6}/)?.[0] || '#7D85CC';
  return colorHex.replace('#', 'rgba(') + ', 0.7)';
};

// 用户提示词接口
interface UserPrompt {
  id: string;
  title: string;
  description?: string;
  type: string;
  category: string;
  created_at?: string;
  updated_at?: string;
  created_by?: string;
}

interface UserPromptDetailViewProps {
  prompt: UserPrompt;
}

/**
 * 用户提示词详情视图组件 - 仅显示介绍信息
 */
export function UserPromptDetailView({ prompt }: UserPromptDetailViewProps) {
  // 提示词类型的信息
  const typeInfo = promptTypeMap[prompt.type as keyof typeof promptTypeMap] || {
    label: '未知类型',
    color: 'bg-gray-500 text-white',
    icon: 'help_outline'
  };
  const typeColor = getTypeColor(prompt.type);

  // 格式化日期显示
  const formatDate = (dateString?: string) => {
    if (!dateString) return '未知时间';
    try {
      return new Date(dateString).toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch {
      return '未知时间';
    }
  };

  return (
    <div className="flex flex-col min-h-[500px]">
      {/* 标题和类型栏 */}
      <div className="flex items-center justify-between mb-6 mt-4">
        <h2 className="text-xl font-medium text-text-dark font-ma-shan">{prompt.title}</h2>
        <div className="flex items-center">
          <span className={`flex items-center px-3 py-1 rounded-full text-sm ${typeInfo.color}`}>
            <span className="material-icons mr-1 text-sm">{typeInfo.icon}</span>
            {typeInfo.label}
          </span>
        </div>
      </div>

      {/* 用户提示词标识 */}
      <div className="mb-4">
        <div className="inline-flex items-center px-3 py-1 bg-gradient-to-r from-green-100 to-green-50 border border-green-200 rounded-full">
          <span className="material-icons text-green-600 text-sm mr-1">verified</span>
          <span className="text-green-700 text-sm font-medium">用户提示词</span>
        </div>
      </div>

      {/* 提示词描述 */}
      <div className="mb-6 flex-grow">
        {prompt.description ? (
          <div className="p-6 bg-white bg-opacity-50 rounded-xl border border-[rgba(120,180,140,0.2)] min-h-[300px]">
            <h3 className="text-lg font-medium text-text-dark mb-4 font-ma-shan">提示词介绍</h3>
            <div className="prose prose-sm max-w-none">
              <p className="whitespace-pre-wrap text-text-medium leading-relaxed">{prompt.description}</p>
            </div>
          </div>
        ) : (
          <div className="p-6 bg-white bg-opacity-50 rounded-xl border border-[rgba(120,180,140,0.2)] text-center min-h-[300px] flex items-center justify-center">
            <div className="text-center">
              <span className="material-icons text-4xl text-text-light mb-2">description</span>
              <p className="text-text-light italic">暂无详细介绍</p>
            </div>
          </div>
        )}
      </div>

      {/* 提示词信息 */}
      <div className="border-t border-[rgba(120,180,140,0.2)] pt-4">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="flex items-center text-text-light">
            <span className="material-icons text-sm mr-2">schedule</span>
            <span>创建时间：{formatDate(prompt.created_at)}</span>
          </div>
          <div className="flex items-center text-text-light">
            <span className="material-icons text-sm mr-2">update</span>
            <span>更新时间：{formatDate(prompt.updated_at)}</span>
          </div>
          {prompt.created_by && (
            <div className="flex items-center text-text-light col-span-2">
              <span className="material-icons text-sm mr-2">person</span>
              <span>创建者：{prompt.created_by}</span>
            </div>
          )}
        </div>
      </div>

      {/* 使用说明 */}
      <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-xl">
        <div className="flex items-start">
          <span className="material-icons text-blue-600 text-sm mr-2 mt-0.5">info</span>
          <div className="text-sm text-blue-700">
            <p className="font-medium mb-1">使用说明</p>
            <p>这是系统提供的提示词模板，您可以在AI写作功能中选择使用。这些提示词经过精心设计和测试，能够帮助您获得更好的AI创作效果。</p>
          </div>
        </div>
      </div>
    </div>
  );
}
