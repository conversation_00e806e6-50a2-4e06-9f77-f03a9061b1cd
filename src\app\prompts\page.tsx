'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

import TopBar from '@/components/TopBar';
import { PromptGroup } from '@/types/ui';
import PromptFormModal from '@/components/prompts/PromptFormModal';
import PromptDeleteModal from '@/components/prompts/PromptDeleteModal';
import PromptReadModal from '@/components/prompts/PromptReadModal';
import { getCurrentUser } from '@/services/userService';

// 提示词类型映射
const promptTypeMap = {
  'ai_writing': { label: 'AI写作', color: 'bg-[#5a9d6b] text-white', icon: 'create', group: 'novel', gradient: 'from-[#5a9d6b] to-[#4a8d5b]' },
  'ai_polishing': { label: 'AI润色', color: 'bg-[#7D85CC] text-white', icon: 'auto_fix_high', group: 'novel', gradient: 'from-[#7D85CC] to-[#6F9CE0]' }
};

// 提示词分组定义
const promptGroups: PromptGroup[] = [
  {
    label: '小说创作',
    color: 'bg-[#5a9d6b] text-white',
    icon: 'auto_stories',
    types: ['ai_writing', 'ai_polishing']
  }
];

export default function PromptsPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [activeGroup, setActiveGroup] = useState(0);
  const [activeType, setActiveType] = useState('');
  const [userPrompts, setUserPrompts] = useState<any[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentUser, setCurrentUser] = useState<any>(null);

  // 模态窗口状态
  const [showFormModal, setShowFormModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showReadModal, setShowReadModal] = useState(false);
  const [editingPrompt, setEditingPrompt] = useState<any>(null);
  const [deletingPrompt, setDeletingPrompt] = useState<any>(null);
  const [readingPrompt, setReadingPrompt] = useState<any>(null);

  // 初始化默认选中的类型和用户信息
  useEffect(() => {
    if (promptGroups.length > 0 && promptGroups[activeGroup].types.length > 0) {
      const defaultType = promptGroups[activeGroup].types[0];
      setActiveType(defaultType);
    }

    // 获取当前用户信息
    const loadCurrentUser = async () => {
      try {
        const user = await getCurrentUser();
        setCurrentUser(user);
      } catch (error) {
        console.error('获取用户信息失败:', error);
      }
    };

    loadCurrentUser();
  }, [activeGroup]);

  // 加载用户提示词数据
  const loadUserPrompts = async (type: string) => {
    try {
      const response = await fetch('/api/prompt', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          category: 'userprompt',
          type: type,
          limit: 50
        })
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setUserPrompts(data.data || []);
        }
      }
    } catch (error) {
      console.error('加载用户提示词失败:', error);
      setUserPrompts([]);
    }
  };

  // 加载提示词数据
  const loadPrompts = async (type: string) => {
    try {
      setIsLoading(true);
      await loadUserPrompts(type);
    } catch (error) {
      console.error('加载提示词失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 当选中类型改变时加载提示词
  useEffect(() => {
    if (activeType) {
      loadPrompts(activeType);
    }
  }, [activeType]);

  // 管理功能处理函数
  const handleCreatePrompt = () => {
    setEditingPrompt(null);
    setShowFormModal(true);
  };

  const handleEditPrompt = (prompt: any) => {
    setEditingPrompt(prompt);
    setShowFormModal(true);
  };

  const handleDeletePrompt = (prompt: any) => {
    setDeletingPrompt(prompt);
    setShowDeleteModal(true);
  };

  const handleReadPrompt = (prompt: any) => {
    setReadingPrompt(prompt);
    setShowReadModal(true);
  };

  const handleSavePrompt = async (savedPrompt: any) => {
    // 重新加载提示词列表
    if (activeType) {
      await loadPrompts(activeType);
    }
  };

  const handleConfirmDelete = async () => {
    if (!deletingPrompt) return;

    try {
      const response = await fetch(`/api/prompt?id=${deletingPrompt.id}`, {
        method: 'DELETE'
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || '删除失败');
      }

      if (data.success) {
        // 重新加载提示词列表
        if (activeType) {
          await loadPrompts(activeType);
        }
        setShowDeleteModal(false);
        setDeletingPrompt(null);
      } else {
        throw new Error(data.error || '删除失败');
      }
    } catch (error) {
      console.error('删除提示词失败:', error);
      alert(error instanceof Error ? error.message : '删除失败，请稍后再试');
    }
  };

  // 检查是否为当前用户创建的提示词
  const isOwnPrompt = (prompt: any) => {
    return currentUser && prompt.created_by === currentUser.email;
  };

  // 处理大分类切换
  const handleGroupChange = (groupIndex: number) => {
    setActiveGroup(groupIndex);
    const newType = promptGroups[groupIndex].types[0];
    setActiveType(newType);
  };

  // 处理具体类型切换
  const handleTypeChange = (type: string) => {
    setActiveType(type);
  };

  // 过滤提示词
  const filteredPrompts = userPrompts.filter(prompt => {
    const matchesSearch =
      prompt.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (prompt.content && prompt.content.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (prompt.description && prompt.description.toLowerCase().includes(searchTerm.toLowerCase()));
    return matchesSearch;
  });



  return (
    <div className="flex h-screen bg-bg-color animate-fadeIn overflow-hidden">
      {/* 背景网格 */}
      <div className="grid-background"></div>

      {/* 装饰元素，在小屏幕上减少数量 */}
      <div className="dot hidden md:block" style={{ top: "120px", left: "15%" }}></div>
      <div className="dot" style={{ bottom: "80px", right: "20%" }}></div>
      <div className="dot hidden md:block" style={{ top: "30%", right: "25%" }}></div>
      <div className="dot hidden md:block" style={{ bottom: "40%", left: "30%" }}></div>

      <svg className="wave hidden md:block" style={{ bottom: "20px", left: "10%" }} width="100" height="20" viewBox="0 0 100 20">
        <path d="M0,10 Q25,0 50,10 T100,10" fill="none" stroke="var(--accent-brown)" strokeWidth="2" />
      </svg>

      <div className="flex-1 flex flex-col overflow-hidden">
        <TopBar
          showBackButton={true}
        />
        {/* 横向分类标签导航 */}
        <div className="border-b border-[rgba(120,180,140,0.15)] bg-gradient-to-r from-white to-gray-50/50 backdrop-blur-sm">
          <div className="w-full px-4 sm:px-6 lg:px-8">
            <div className="flex items-center">
              {/* 左边：大分类选择器 */}
              <div className="flex space-x-1">
                {promptGroups.map((group, index) => (
                  <button
                    key={group.label}
                    className={`relative px-6 py-4 text-sm font-medium transition-all duration-300 rounded-t-xl transform hover:scale-105 ${
                      activeGroup === index
                        ? 'bg-gradient-to-b from-[#00C250] to-[#00A844] text-white shadow-lg border-b-2 border-[#00C250]'
                        : 'text-gray-600 hover:text-gray-800 hover:bg-white/60 border-b-2 border-transparent hover:border-[rgba(120,180,140,0.3)]'
                    }`}
                    onClick={() => handleGroupChange(index)}
                  >
                    <div className="flex items-center relative z-10">
                      <div className={`w-6 h-6 rounded-full flex items-center justify-center mr-2 ${
                        activeGroup === index
                          ? 'bg-white/20'
                          : 'bg-gradient-to-br from-gray-100 to-gray-200'
                      }`}>
                        <span className={`material-icons text-sm ${
                          activeGroup === index ? 'text-white' : 'text-gray-600'
                        }`}>
                          {group.icon}
                        </span>
                      </div>
                      <span>{group.label}</span>
                    </div>
                    {activeGroup === index && (
                      <div className="absolute inset-0 bg-gradient-to-b from-white/20 to-transparent rounded-t-xl"></div>
                    )}
                  </button>
                ))}
              </div>

              {/* 分隔符 */}
              <div className="h-6 w-px bg-gradient-to-b from-transparent via-[rgba(120,180,140,0.3)] to-transparent mx-6"></div>

              {/* 右边：当前大分类下的具体类型选项 */}
              <div className="flex space-x-2 flex-1">
                {promptGroups[activeGroup].types.map(type => (
                  <button
                    key={type}
                    className={`relative px-4 py-2 text-sm font-medium transition-all duration-300 rounded-full transform hover:scale-105 ${
                      activeType === type
                        ? 'bg-gradient-to-r from-[#00C250]/20 to-[#00A844]/20 text-[#00C250] border-2 border-[#00C250]/30 shadow-md'
                        : 'text-gray-600 hover:text-gray-800 hover:bg-white/60 border-2 border-transparent hover:border-[rgba(120,180,140,0.2)]'
                    }`}
                    onClick={() => handleTypeChange(type)}
                  >
                    <div className="flex items-center relative z-10">
                      <div className={`w-5 h-5 rounded-full flex items-center justify-center mr-2 ${
                        activeType === type
                          ? 'bg-[#00C250]/20'
                          : 'bg-gradient-to-br from-gray-100 to-gray-200'
                      }`}>
                        <span className={`material-icons text-xs ${
                          activeType === type ? 'text-[#00C250]' : 'text-gray-600'
                        }`}>
                          {promptTypeMap[type as keyof typeof promptTypeMap]?.icon}
                        </span>
                      </div>
                      <span>{promptTypeMap[type as keyof typeof promptTypeMap]?.label}</span>
                    </div>
                    {activeType === type && (
                      <div className="absolute inset-0 bg-gradient-to-r from-[#00C250]/10 to-[#00A844]/10 rounded-full animate-pulse"></div>
                    )}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        <main className="flex-1 overflow-auto p-4 sm:p-6 lg:p-8">
          <div className="w-full">
            {/* 工具栏：搜索框和创建按钮 */}
            <div className="relative mb-8 flex flex-wrap items-center gap-6 justify-between">
              {/* 搜索框 */}
              <div className="flex-shrink-0 w-80">
                <div className="relative w-full">
                  <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10">
                    <span className="material-icons text-gray-400 transition-colors duration-300">search</span>
                  </div>
                  <input
                    type="text"
                    className="block w-full pl-12 pr-4 py-3 bg-white/80 backdrop-blur-sm border border-[rgba(120,180,140,0.2)] rounded-xl focus:outline-none focus:ring-2 focus:ring-[rgba(90,157,107,0.3)] focus:border-[rgba(90,157,107,0.4)] focus:bg-white/95 transition-all duration-300 text-gray-700 placeholder-gray-400 shadow-sm hover:shadow-md font-medium"
                    placeholder="搜索提示词标题或描述..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                  {searchTerm && (
                    <button
                      onClick={() => setSearchTerm('')}
                      className="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-gray-600 transition-colors duration-300"
                    >
                      <span className="material-icons text-sm">clear</span>
                    </button>
                  )}
                </div>
              </div>

              {/* 创建按钮 */}
              {currentUser && (
                <button
                  onClick={handleCreatePrompt}
                  className="group relative flex items-center px-6 py-3 bg-gradient-to-r from-[#00C250] to-[#00A844] text-white rounded-xl hover:from-[#00A844] hover:to-[#008A3A] transition-all duration-300 transform hover:scale-105 hover:shadow-lg hover:shadow-[rgba(0,194,80,0.3)] font-medium"
                >
                  <span className="material-icons text-sm mr-2 group-hover:rotate-90 transition-transform duration-300">add</span>
                  <span>创建提示词</span>
                  <div className="absolute inset-0 bg-white/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </button>
              )}
            </div>

            {/* 提示词卡片网格 */}
            <div className="flex flex-wrap gap-6">
              {filteredPrompts.length > 0 ? (
                filteredPrompts.map((prompt) => {
                  const typeConfig = promptTypeMap[activeType as keyof typeof promptTypeMap];
                  if (!typeConfig) return null;

                  const colorText = typeConfig.color.split(' ')[1];
                  const bgColor = typeConfig.color.split(' ')[0];

                  return (
                    <div
                      key={prompt.id}
                      className="group relative cursor-pointer animate-fadeIn transform transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1"
                      style={{
                        width: '320px',
                        height: '340px',
                        animationDelay: `${Math.random() * 200}ms`
                      }}
                      onClick={() => handleReadPrompt(prompt)}
                    >
                      {/* 主卡片容器 */}
                      <div className="relative h-full bg-gradient-to-br from-white to-gray-50/80 backdrop-blur-sm rounded-2xl border border-[rgba(120,180,140,0.2)] shadow-lg hover:shadow-xl hover:shadow-[rgba(120,180,140,0.15)] transition-all duration-300 overflow-hidden">

                        {/* 背景装饰 */}
                        <div className="absolute inset-0 bg-gradient-to-br from-[rgba(120,180,140,0.02)] to-[rgba(90,157,107,0.04)] opacity-60"></div>
                        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-[rgba(120,180,140,0.08)] to-transparent rounded-full transform translate-x-16 -translate-y-16"></div>

                        {/* 胶带装饰 */}
                        <div className="absolute -top-2 left-8 w-16 h-6 bg-gradient-to-r from-yellow-300/70 to-yellow-400/70 rounded-sm transform -rotate-3 shadow-sm z-10"></div>

                        <div className="relative z-10 flex flex-col h-full p-6">
                          {/* 顶部LOGO和标题区域 */}
                          <div className="flex items-start mb-4">
                            <div className={`relative w-14 h-14 rounded-2xl ${bgColor} flex items-center justify-center mr-4 shadow-lg group-hover:shadow-xl transition-all duration-300`}>
                              <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-2xl"></div>
                              <span className={`material-icons text-2xl ${colorText} relative z-10 group-hover:scale-110 transition-transform duration-300`}>
                                {typeConfig.icon}
                              </span>
                              <div className="absolute inset-0 bg-gradient-to-br from-transparent to-black/5 rounded-2xl"></div>
                            </div>
                            <div className="flex-1 min-w-0">
                              <h3 className="font-semibold text-gray-800 text-lg font-ma-shan leading-tight mb-1 group-hover:text-gray-900 transition-colors duration-300">
                                {prompt.title}
                              </h3>
                              <div className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium shadow-sm ${
                                activeType === 'ai_writing'
                                  ? 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-700 border border-green-200/50'
                                  : 'bg-gradient-to-r from-purple-100 to-indigo-100 text-purple-700 border border-purple-200/50'
                              }`}>
                                <span className="material-icons text-xs mr-1">
                                  {typeConfig.icon}
                                </span>
                                {typeConfig.label}
                              </div>
                            </div>

                            {/* 管理按钮 - 只对自己创建的提示词显示 */}
                            {isOwnPrompt(prompt) && (
                              <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-1 group-hover:translate-y-0">
                                <div className="flex space-x-1 bg-white/90 backdrop-blur-sm rounded-lg p-1 shadow-lg border border-gray-200/50">
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleEditPrompt(prompt);
                                    }}
                                    className="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-all duration-200 transform hover:scale-110"
                                    title="编辑"
                                  >
                                    <span className="material-icons text-sm">edit</span>
                                  </button>
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleDeletePrompt(prompt);
                                    }}
                                    className="p-2 text-gray-500 hover:text-red-600 hover:bg-red-50 rounded-md transition-all duration-200 transform hover:scale-110"
                                    title="删除"
                                  >
                                    <span className="material-icons text-sm">delete</span>
                                  </button>
                                </div>
                              </div>
                            )}
                          </div>

                          {/* 描述内容 */}
                          <div className="flex-1 mb-4">
                            <p className="text-gray-600 text-sm leading-relaxed line-clamp-4 group-hover:text-gray-700 transition-colors duration-300">
                              {prompt.description || '这个提示词暂时没有描述信息...'}
                            </p>
                          </div>

                          {/* 底部信息栏 */}
                          <div className="mt-auto">
                            <div className="flex items-center justify-between pt-4 border-t border-gray-200/60">
                              {/* 左侧：使用次数统计 */}
                              <div className="flex items-center px-3 py-1.5 bg-gradient-to-r from-orange-50 to-amber-50 border border-orange-200/50 rounded-full shadow-sm">
                                <div className="w-4 h-4 bg-gradient-to-br from-orange-100 to-amber-100 rounded-full flex items-center justify-center mr-2">
                                  <span className="material-icons text-xs text-orange-600">trending_up</span>
                                </div>
                                <span className="font-medium text-orange-700 text-xs">
                                  已使用 {(prompt as any).usage_count || 0} 次
                                </span>
                              </div>

                              {/* 右侧：作者名和日期（两行） */}
                              <div className="flex flex-col text-xs text-gray-500 space-y-3">
                                {/* 第一行：作者 */}
                                <div className="flex items-center">
                                  <div className="w-4 h-4 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full flex items-center justify-center">
                                    <span className="material-icons text-xs text-blue-600">person</span>
                                  </div>
                                  <span className="font-medium truncate max-w-20 ml-2">
                                    {prompt.author_display_id || '未知用户'}
                                  </span>
                                </div>

                                {/* 第二行：日期 */}
                                <div className="flex items-center">
                                  <div className="w-4 h-4 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center">
                                    <span className="material-icons text-xs text-gray-600">schedule</span>
                                  </div>
                                  <span className="font-medium ml-2">
                                    {new Date(prompt.updated_at).toLocaleDateString('zh-CN', {
                                      month: 'short',
                                      day: 'numeric'
                                    })}
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* 翻页效果 */}
                        <div className="absolute bottom-0 right-0 w-8 h-8 bg-gradient-to-tl from-gray-200/50 to-transparent transform rotate-45 translate-x-4 translate-y-4 group-hover:from-gray-300/60 transition-all duration-300"></div>
                      </div>
                    </div>
                  );
                })
              ) : (
                // 无提示词提示
                <div className="w-full flex flex-col items-center justify-center py-16 px-8">
                  <div className="relative mb-8">
                    {/* 主图标容器 */}
                    <div className="w-32 h-32 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center shadow-lg border border-gray-200/50">
                      <div className="w-24 h-24 bg-gradient-to-br from-white to-gray-50 rounded-full flex items-center justify-center shadow-inner">
                        <span className="material-icons text-5xl text-gray-400">
                          {searchTerm ? 'search_off' : 'lightbulb_outline'}
                        </span>
                      </div>
                    </div>

                    {/* 装饰元素 */}
                    <div className="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-br from-yellow-300 to-yellow-400 rounded-full flex items-center justify-center shadow-md animate-bounce">
                      <span className="material-icons text-sm text-yellow-800">star</span>
                    </div>
                    <div className="absolute -bottom-1 -left-1 w-6 h-6 bg-gradient-to-br from-blue-300 to-blue-400 rounded-full flex items-center justify-center shadow-md">
                      <span className="material-icons text-xs text-blue-800">add</span>
                    </div>
                  </div>

                  <div className="text-center max-w-md">
                    <h3 className="text-2xl font-bold text-gray-700 mb-3 font-ma-shan">
                      {searchTerm ? "没有找到匹配的提示词" : "暂无提示词"}
                    </h3>
                    <p className="text-gray-500 leading-relaxed mb-8">
                      {searchTerm
                        ? `没有找到包含"${searchTerm}"的提示词，尝试使用其他关键词搜索`
                        : `暂无${promptTypeMap[activeType as keyof typeof promptTypeMap]?.label}类型的用户提示词，点击创建按钮开始添加您的第一个提示词`}
                    </p>

                    <div className="flex flex-col sm:flex-row gap-3 justify-center">
                      {searchTerm ? (
                        <button
                          className="px-6 py-3 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-xl hover:from-gray-600 hover:to-gray-700 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl font-medium"
                          onClick={() => setSearchTerm('')}
                        >
                          <span className="material-icons text-sm mr-2">clear</span>
                          清除搜索
                        </button>
                      ) : (
                        currentUser && (
                          <button
                            className="px-8 py-3 bg-gradient-to-r from-[#00C250] to-[#00A844] text-white rounded-xl hover:from-[#00A844] hover:to-[#008A3A] transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl font-medium"
                            onClick={handleCreatePrompt}
                          >
                            <span className="material-icons text-sm mr-2">add_circle</span>
                            创建第一个提示词
                          </button>
                        )
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </main>


      </div>

      {/* 提示词表单模态窗口 */}
      <PromptFormModal
        isOpen={showFormModal}
        onClose={() => {
          setShowFormModal(false);
          setEditingPrompt(null);
        }}
        onSave={handleSavePrompt}
        prompt={editingPrompt}
        promptType={activeType}
      />

      {/* 删除确认模态窗口 */}
      <PromptDeleteModal
        isOpen={showDeleteModal}
        onClose={() => {
          setShowDeleteModal(false);
          setDeletingPrompt(null);
        }}
        onConfirm={handleConfirmDelete}
        prompt={deletingPrompt}
      />

      {/* 提示词阅读模态窗口 */}
      <PromptReadModal
        isOpen={showReadModal}
        onClose={() => {
          setShowReadModal(false);
          setReadingPrompt(null);
        }}
        prompt={readingPrompt}
      />
    </div>
  );
}
