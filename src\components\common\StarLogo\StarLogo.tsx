import React from 'react';
import styles from './StarLogo.module.css';
import { StarLogoProps } from './StarLogo.types';

const StarLogo: React.FC<StarLogoProps> = ({ 
  size = 48, 
  className = '', 
  onClick 
}) => {
  const customStyle = size !== 48 ? { '--logo-size': `${size}px` } as React.CSSProperties : {};

  return (
    <div 
      className={`${styles.logoIcon} ${className}`}
      style={customStyle}
      data-size={size !== 48 ? size : undefined}
      onClick={onClick}
    >
      <div className={styles.logoStars}>
        <div className={styles.centerStar}></div>
        <div className={styles.orbitStar}></div>
        <div className={styles.orbitStar}></div>
        <div className={styles.orbitStar}></div>
        <div className={styles.orbitStar}></div>
        <div className={styles.orbitStar}></div>
        <div className={styles.orbitStar}></div>
        <div className={styles.orbitStar}></div>
        <div className={styles.orbitStar}></div>
      </div>
    </div>
  );
};

export default StarLogo;
