/**
 * MinIO连接测试API
 * 用于测试MinIO服务器连接是否正常
 */
import { NextRequest, NextResponse } from 'next/server';
import { minioClient, MINIO_BUCKET, ensureBucketExists } from '@/lib/minio';

/**
 * GET 方法 - 测试MinIO连接
 */
export async function GET(request: NextRequest) {
  try {
    console.log('开始测试MinIO连接...');

    // 测试1: 检查MinIO客户端配置
    console.log('MinIO配置:', {
      endPoint: process.env.MINIO_ENDPOINT,
      port: process.env.MINIO_PORT,
      useSSL: process.env.MINIO_USE_SSL,
      bucket: MINIO_BUCKET
    });

    // 测试2: 检查存储桶是否存在
    console.log('检查存储桶是否存在...');
    const bucketExists = await minioClient.bucketExists(MINIO_BUCKET);
    console.log(`存储桶 ${MINIO_BUCKET} 存在:`, bucketExists);

    // 测试3: 如果不存在则创建存储桶
    if (!bucketExists) {
      console.log('创建存储桶...');
      await ensureBucketExists();
    }

    // 测试4: 列出存储桶中的对象（限制10个）
    console.log('列出存储桶中的对象...');
    const objects: any[] = [];
    const stream = minioClient.listObjects(MINIO_BUCKET, '', true);
    
    let count = 0;
    for await (const obj of stream) {
      objects.push({
        name: obj.name,
        size: obj.size,
        lastModified: obj.lastModified
      });
      count++;
      if (count >= 10) break; // 限制只显示前10个对象
    }

    console.log('MinIO连接测试成功');

    return NextResponse.json({
      success: true,
      message: 'MinIO连接测试成功',
      data: {
        bucketExists,
        bucketName: MINIO_BUCKET,
        objectCount: objects.length,
        sampleObjects: objects,
        config: {
          endPoint: process.env.MINIO_ENDPOINT,
          port: process.env.MINIO_PORT,
          useSSL: process.env.MINIO_USE_SSL === 'true'
        }
      }
    });

  } catch (error) {
    console.error('MinIO连接测试失败:', error);
    
    return NextResponse.json(
      { 
        success: false,
        error: 'MinIO连接测试失败',
        details: error instanceof Error ? error.message : '未知错误',
        config: {
          endPoint: process.env.MINIO_ENDPOINT,
          port: process.env.MINIO_PORT,
          useSSL: process.env.MINIO_USE_SSL === 'true',
          bucket: MINIO_BUCKET
        }
      },
      { status: 500 }
    );
  }
}
