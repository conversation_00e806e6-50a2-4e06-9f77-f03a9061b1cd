/**
 * 文件列表API
 * 获取用户的所有文件列表，支持智能同步的时间比较功能
 */
import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';

// Supabase配置
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

/**
 * 服务器端获取当前用户
 */
async function getCurrentUser(request: NextRequest) {
  try {
    const supabaseServer = createServerClient(
      supabaseUrl,
      supabaseAnonKey,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll().map(cookie => ({
              name: cookie.name,
              value: cookie.value
            }));
          },
          setAll() {
            // 在API路由中不需要设置cookies
          },
        },
      }
    );

    const { data: { user }, error } = await supabaseServer.auth.getUser();

    if (error) {
      console.error('获取用户失败:', error);
      return null;
    }

    return user;
  } catch (error) {
    console.error('获取用户异常:', error);
    return null;
  }
}

/**
 * GET 方法 - 获取文件列表
 */
export async function GET(request: NextRequest) {
  try {
    // 获取当前用户
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json(
        { error: '用户未登录' },
        { status: 401 }
      );
    }

    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const workTitle = searchParams.get('workTitle');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const includeWorkComparison = searchParams.get('includeWorkComparison') === 'true';
    const offset = (page - 1) * limit;

    // 创建Supabase客户端
    const supabaseServer = createServerClient(
      supabaseUrl,
      supabaseAnonKey,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll().map(cookie => ({
              name: cookie.name,
              value: cookie.value
            }));
          },
          setAll() {},
        },
      }
    );

    // 构建查询
    let query = supabaseServer
      .from('novel_files')
      .select('*', { count: 'exact' })
      .eq('user_id', user.id)
      .order('upload_time', { ascending: false });

    // 如果指定了作品标题，则过滤
    if (workTitle) {
      query = query.eq('work_title', workTitle);
    }

    // 分页
    query = query.range(offset, offset + limit - 1);

    const { data: files, error, count } = await query;

    if (error) {
      console.error('查询文件列表失败:', error);
      return NextResponse.json(
        { error: '查询文件列表失败' },
        { status: 500 }
      );
    }

    console.log(`获取文件列表成功，用户: ${user.email}, 文件数量: ${files?.length || 0}`);

    // 如果需要包含作品比较信息，则处理文件数据
    let processedFiles = files || [];
    if (includeWorkComparison && files && files.length > 0) {
      // 为每个文件添加同步状态信息
      processedFiles = files.map(file => ({
        ...file,
        sync_status: 'cloud_only', // 默认状态，前端会根据本地数据进行比较
        upload_time_iso: file.upload_time // 确保时间格式一致
      }));
    }

    return NextResponse.json({
      success: true,
      data: {
        files: processedFiles,
        pagination: {
          page,
          limit,
          total: count || 0,
          totalPages: Math.ceil((count || 0) / limit)
        },
        includeWorkComparison
      }
    });

  } catch (error) {
    console.error('获取文件列表失败:', error);
    return NextResponse.json(
      { 
        error: '获取文件列表失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
