/**
 * AI辅助弹窗组件 - 吉卜力风格
 */
import React, { useState, useEffect, useRef } from 'react';
import { Modal } from '@/components/common/modals';
import { getKnowledgeByWorkId } from '@/data';
import { Knowledge } from '@/data';
import { generateAIContentStream, MODELS, Message, Usage } from '@/lib/AIserver';
import { BillingService } from '@/lib/billing';
import { AIKnowledgeSelectionModal } from '@/components/knowledgebase/AIKnowledgeSelectionModal'; // 导入简化版知识库选择窗口
import { AICharacterSelectionModal } from '@/components/works/AICharacterSelectionModal'; // 导入角色卡选择组件
import { ChapterAssociationModal } from '@/components/works/ChapterAssociationModal'; // 导入章节关联组件

import { PromptSelectionModal } from '@/components/prompts/PromptSelectionModal'; // 导入提示词选择组件
import { Character } from '@/types/character'; // 导入角色类型
import { Chapter } from '@/types/chapter'; // 导入章节类型
// 本地提示词store已删除
import { getCurrentUser } from '@/services/userService';
import StarLogo from '@/components/common/StarLogo/StarLogo';

// 用户提示词接口
interface UserPrompt {
  id: string;
  title: string;
  description?: string;
  type: string;
  content?: string;
  created_at: string;
  updated_at: string;
  created_by?: string;
  usage_count?: number;
}



// 组件属性
interface AIAssistantModalProps {
  isOpen: boolean;
  onClose: () => void;
  onInsertToEditor: (content: string) => void;
  currentContent: string;
  chapters?: Chapter[];
  activeChapter?: number;
  initialPromptType?: 'ai_writing' | 'ai_polishing';
  workId?: number; // 添加作品ID
  defaultIsDescending?: boolean; // 默认排序状态
}

/**
 * AI辅助弹窗组件
 */
export const AIAssistantModal: React.FC<AIAssistantModalProps> = ({
  isOpen,
  onClose,
  onInsertToEditor,
  currentContent,
  chapters = [],
  activeChapter = 0,
  initialPromptType = 'ai_writing',
  workId, // 接收 workId
  defaultIsDescending = false // 默认排序状态
}) => {
  // 基本状态 - 只保留用户提示词
  const [userPrompts, setUserPrompts] = useState<UserPrompt[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedContent, setGeneratedContent] = useState('');
  const [error, setError] = useState('');
  const [showGenerationView, setShowGenerationView] = useState(false);
  const [hasReturnedFromGeneration, setHasReturnedFromGeneration] = useState(false);
  const [isButtonCooldown, setIsButtonCooldown] = useState(false); // 按钮冷却状态
  const [elapsedTime, setElapsedTime] = useState(0); // 计时器状态
  const [showChapterModal, setShowChapterModal] = useState(false);
  const [previewedChapterIndex, setPreviewedChapterIndex] = useState<number | null>(null); // 新增：用于预览的章节索引
  const [wordCount, setWordCount] = useState(0); // 新增：字数统计状态
  const [usage, setUsage] = useState<Usage | null>(null); // 新增：usage统计状态
  const [consumedWords, setConsumedWords] = useState(0); // 新增：消耗字数状态

  // 移除了生成速度相关的状态: generationStartTime, generationSpeed, hasReceivedContent, lastSpeedUpdateRef

  // 计时器效果
  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;

    if (isGenerating) {
      // 重置计时器
      setElapsedTime(0);
      // 启动计时器，每100ms更新一次
      interval = setInterval(() => {
        setElapsedTime(prev => prev + 0.1);
      }, 100);
    } else {
      // 停止计时器
      if (interval) {
        clearInterval(interval);
      }
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [isGenerating]);

  // 知识库相关状态
  const [showKnowledgeModal, setShowKnowledgeModal] = useState(false);
  const [selectedKnowledge, setSelectedKnowledge] = useState<Knowledge[]>([]);
  const [availableKnowledge, setAvailableKnowledge] = useState<Knowledge[]>([]);

  // 角色卡相关状态
  const [showCharacterModal, setShowCharacterModal] = useState(false);
  const [selectedCharacters, setSelectedCharacters] = useState<Character[]>([]);
  const [availableCharacters, setAvailableCharacters] = useState<Character[]>([]);

  // 提示词选择相关状态
  const [showPromptSelectionModal, setShowPromptSelectionModal] = useState(false);

  // 自定义提示词相关状态
  const [isCustomPromptMode, setIsCustomPromptMode] = useState(false);
  const [customPromptContent, setCustomPromptContent] = useState('');



  // 本地存储键名常量
  const LAST_USED_AI_FUNCTION_KEY = 'lastUsedAIFunction';
  const LAST_USED_AI_MODEL_KEY = 'lastUsedAIModel';

  // 当前选择的提示词类型 - 从本地存储读取上次选择
  const [promptType, setPromptType] = useState<'ai_writing' | 'ai_polishing'>(() => {
    // 如果有初始类型且是润色（从选中文本进入），则使用润色但不影响localStorage中的记忆
    if (initialPromptType === 'ai_polishing' && currentContent && currentContent.trim() !== '') {
      return 'ai_polishing';
    }

    // 如果是通过AI写作按钮打开的（initialPromptType为默认值'ai_writing'），则从本地存储读取上次选择
    if (initialPromptType === 'ai_writing') {
      if (typeof window !== 'undefined') {
        const lastFunction = localStorage.getItem(LAST_USED_AI_FUNCTION_KEY);
        // 如果有记录且是有效的选项，则使用它
        if (lastFunction && ['ai_writing', 'ai_polishing'].includes(lastFunction)) {
          return lastFunction as 'ai_writing' | 'ai_polishing';
        }
      }


    // 默认使用AI写作
    return 'ai_writing';
  });

  // 全局AI模型选择状态 - 从localStorage读取上次选择
  const [globalSelectedModel, setGlobalSelectedModel] = useState<string>(() => {
    if (typeof window !== 'undefined') {
      const lastModel = localStorage.getItem(LAST_USED_AI_MODEL_KEY);
      if (lastModel && [MODELS.LLM_TEST, MODELS.LLM_CLAUDE].includes(lastModel)) {
        return lastModel;
      }
    }
    return MODELS.LLM_TEST; // 默认使用测试版
  });

  // 每种类型的记忆状态
  const [typeMemory, setTypeMemory] = useState<{
    [key in 'ai_writing' | 'ai_polishing']: {
      selectedPrompt: UserPrompt | null;
      selectedChapters: number[];
      selectedKnowledgeIds: number[]; // 重命名为选中的知识条目ID数组
      selectedCharacterIds: string[]; // 添加选中的角色ID数组
      userInput: string; // 添加用户输入
    }
  }>({
    'ai_writing': {
      selectedPrompt: null,
      selectedChapters: [],
      selectedKnowledgeIds: [],
      selectedCharacterIds: [],
      userInput: ''
    },
    'ai_polishing': {
      selectedPrompt: null,
      selectedChapters: [],
      selectedKnowledgeIds: [],
      selectedCharacterIds: [],
      userInput: ''
    }
  });

  // 当前类型的状态
  const selectedPrompt = typeMemory[promptType].selectedPrompt;
  const selectedModel = globalSelectedModel; // 使用全局AI模型选择
  const selectedChapters = typeMemory[promptType].selectedChapters;
  const selectedKnowledgeIds = typeMemory[promptType].selectedKnowledgeIds; // 重命名为 selectedKnowledgeIds
  const selectedCharacterIds = typeMemory[promptType].selectedCharacterIds; // 解构选中的角色ID
  const userInput = typeMemory[promptType].userInput; // 解构用户输入

  // 创建一个滚动容器引用，用于整个可滚动区域
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  // 生成结果容器的引用
  const resultContainerRef = useRef<HTMLDivElement>(null);

  // 在组件顶部添加新的状态和处理函数，在适当的位置添加
  // Removed the useEffect mount log as it wasn't appearing reliably.
  const [editedChapterContent, setEditedChapterContent] = useState<string>('');

  // 自动关联状态
  const [isAutoAssociate, setIsAutoAssociate] = useState<boolean>(false);
  const [autoAssociateCount, setAutoAssociateCount] = useState<number>(5); // 默认前5章

  // 章节排序状态
  const [isDescending, setIsDescending] = useState<boolean>(defaultIsDescending); // 使用传入的默认排序状态

  // 监听父组件传入的排序状态变化
  useEffect(() => {
    setIsDescending(defaultIsDescending);
  }, [defaultIsDescending]);

  // 在章节关联窗口打开时，确保排序状态与父组件一致
  useEffect(() => {
    if (showChapterModal) {
      setIsDescending(defaultIsDescending);
    }
  }, [showChapterModal, defaultIsDescending]);

  // 监听章节数量变化，如果开启了自动关联，则自动更新关联章节
  useEffect(() => {
    // 当章节数量变化且开启了自动关联时，自动更新关联章节
    if (isAutoAssociate && chapters.length > 0 && autoAssociateCount > 0) {
      // 使用当前的自动关联设置更新章节关联
      // 定义一个内部函数来处理章节关联，避免依赖项问题
      const updateChapterAssociation = () => {
        // 根据排序状态决定要选择的章节
        let chaptersToCheck = [];
        const count = autoAssociateCount;

        if (isDescending) {
          // 倒序时，选择最后 count 章
          const startIndex = Math.max(0, chapters.length - count);
          for (let i = startIndex; i < chapters.length; i++) {
            chaptersToCheck.push(i);
          }
        } else {
          // 正序时，选择前 count 章
          const endIndex = Math.min(count - 1, chapters.length - 1);
          for (let i = 0; i <= endIndex; i++) {
            chaptersToCheck.push(i);
          }
        }

        // 检查当前选中的章节是否就是要选择的章节
        const currentSelectedChapters = typeMemory[promptType].selectedChapters;
        const isAlreadySelected = chaptersToCheck.every(index => currentSelectedChapters.includes(index)) &&
                                  currentSelectedChapters.length === chaptersToCheck.length;

        if (!isAlreadySelected) {
          // 更新选中的章节
          updateTypeMemory({ selectedChapters: chaptersToCheck });
          console.log('章节数量变化，自动更新关联章节:', chaptersToCheck);
        }
      };

      // 执行更新
      updateChapterAssociation();
    }
  }, [chapters.length, isAutoAssociate, isDescending, autoAssociateCount, typeMemory, promptType]);

  // 预览章节时加载内容到编辑器
  useEffect(() => {
    if (previewedChapterIndex !== null && chapters[previewedChapterIndex]) {
      setEditedChapterContent(chapters[previewedChapterIndex].content || '');
    }
  }, [previewedChapterIndex, chapters]);

  // 处理章节内容变化的函数
  const handleChapterContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setEditedChapterContent(e.target.value);
  };

  // 更新当前类型的状态
  const updateTypeMemory = (updates: Partial<{
    selectedPrompt: UserPrompt | null;
    selectedChapters: number[];
    selectedKnowledgeIds: number[];
    selectedCharacterIds: string[];
    userInput: string;
  }>) => {
    setTypeMemory(prev => ({
      ...prev,
      [promptType]: {
        ...prev[promptType],
        ...updates
      }
    }));

    // 当用户修改选择时，清除错误信息
    if (error) {
      setError('');
    }
  };

  // 更新全局AI模型选择
  const updateGlobalSelectedModel = (model: string) => {
    setGlobalSelectedModel(model);
    // 保存到localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem(LAST_USED_AI_MODEL_KEY, model);
    }
  };

  // 打开章节选择器模态窗口
  const openChapterModal = () => {
    // 确保排序状态与父组件一致
    setIsDescending(defaultIsDescending);
    setShowChapterModal(true);
  };

  // 关闭章节选择器模态窗口
  const closeChapterModal = () => {
    setShowChapterModal(false);
  };

  // 处理章节选择
  const handleChapterSelect = (index: number) => {
    // 如果开启了自动关联，则在选择章节后自动应用自动关联
    if (isAutoAssociate) {
      // 先更新选中状态
      updateTypeMemory({
        selectedChapters: selectedChapters.includes(index)
          ? selectedChapters.filter(i => i !== index) // 如果已经选中，则移除
          : [...selectedChapters, index] // 否则添加
      });

      // 然后应用自动关联
      setTimeout(() => {
        associateFirstNChapters(autoAssociateCount);
      }, 0);
    } else {
      // 如果没有开启自动关联，则正常更新选中状态
      updateTypeMemory({
        selectedChapters: selectedChapters.includes(index)
          ? selectedChapters.filter(i => i !== index) // 如果已经选中，则移除
          : [...selectedChapters, index] // 否则添加
      });
    }
  };

  // 确认章节选择
  const confirmChapterSelection = (newSelectedChapters: number[], newIsAutoAssociate: boolean, newAutoAssociateCount: number) => {
    // 更新选中的章节
    updateTypeMemory({
      selectedChapters: newSelectedChapters
    });

    // 更新自动关联状态
    setIsAutoAssociate(newIsAutoAssociate);
    setAutoAssociateCount(newAutoAssociateCount);

    closeChapterModal();

    // 设置按钮冷却状态，3秒内无法点击生成按钮
    setIsButtonCooldown(true);
    setTimeout(() => {
      setIsButtonCooldown(false);
    }, 3000);

    // 保存当前的章节关联和知识条目关联状态到localStorage
    try {
      const memoryToSave = {
        selectedPrompt: typeMemory[promptType].selectedPrompt,
        selectedChapters: newSelectedChapters,
        selectedKnowledgeIds: typeMemory[promptType].selectedKnowledgeIds,
        selectedCharacterIds: typeMemory[promptType].selectedCharacterIds,
        isAutoAssociate: newIsAutoAssociate,
        autoAssociateCount: newAutoAssociateCount
      };
      localStorage.setItem(`ai_assistant_${promptType}_memory`, JSON.stringify(memoryToSave));
    } catch (error) {
      console.error('保存章节关联状态失败:', error);
    }

    console.log('章节关联已更新:', newSelectedChapters, '自动关联:', newIsAutoAssociate, '关联数量:', newAutoAssociateCount);
  };

  // 关联前N章
  const associateFirstNChapters = (count: number) => {
    if (chapters.length === 0) return;

    // 根据排序状态决定要选择的章节
    let chaptersToCheck = [];

    if (isDescending) {
      // 倒序时，选择最后 count 章
      const startIndex = Math.max(0, chapters.length - count);
      for (let i = startIndex; i < chapters.length; i++) {
        chaptersToCheck.push(i);
      }
    } else {
      // 正序时，选择前 count 章
      const endIndex = Math.min(count - 1, chapters.length - 1);
      for (let i = 0; i <= endIndex; i++) {
        chaptersToCheck.push(i);
      }
    }

    // 检查当前选中的章节是否就是要选择的章节
    const isAlreadySelected = chaptersToCheck.every(index => selectedChapters.includes(index)) &&
                              selectedChapters.length === chaptersToCheck.length;

    if (isAlreadySelected) {
      // 如果已经选中了这些章节，则取消选中
      updateTypeMemory({ selectedChapters: [] });
    } else {
      // 否则选中这些章节
      updateTypeMemory({ selectedChapters: chaptersToCheck });
    }

    // 更新自动关联计数
    setAutoAssociateCount(count);
  };

  // 切换自动关联状态
  const toggleAutoAssociate = () => {
    const newState = !isAutoAssociate;
    setIsAutoAssociate(newState);

    // 如果开启自动关联，立即应用当前设置
    if (newState) {
      if (autoAssociateCount > 0) {
        associateFirstNChapters(autoAssociateCount);
      }
    } else {
      // 如果关闭自动关联，清除关联计数
      setAutoAssociateCount(0);
    }
  };

  // 选择知识
  const handleKnowledgeSelect = (knowledge: Knowledge) => {
    const knowledgeId = knowledge.id;
    if (knowledgeId === undefined) return; // ID 不存在则不处理

    const currentSelectedIds = [...typeMemory[promptType].selectedKnowledgeIds];
    const index = currentSelectedIds.indexOf(knowledgeId);

    let updatedSelectedIds: number[];
    let updatedSelectedKnowledge: Knowledge[];

    if (index === -1) {
      // 添加
      updatedSelectedIds = [...currentSelectedIds, knowledgeId];
      updatedSelectedKnowledge = [...selectedKnowledge, knowledge];
    } else {
      // 移除
      updatedSelectedIds = currentSelectedIds.filter(id => id !== knowledgeId);
      updatedSelectedKnowledge = selectedKnowledge.filter((a: Knowledge) => a.id !== knowledgeId);
    }

    // 更新状态
    updateTypeMemory({ selectedKnowledgeIds: updatedSelectedIds });
    setSelectedKnowledge(updatedSelectedKnowledge); // 同时更新界面显示的选中知识条目列表
  };

  // 确认知识条目选择并关闭模态窗口
  const confirmKnowledgeSelection = () => {
    closeKnowledgeModal();
  };

  // 打开知识库模态窗口
  const openKnowledgeModal = () => {
    setShowKnowledgeModal(true);

    // 加载当前作品的知识
    const loadKnowledge = async () => {
      if (workId) {
        try {
          const knowledgeItems = await getKnowledgeByWorkId(workId);
          setAvailableKnowledge(knowledgeItems);

          // 更新已选择的知识
          if (selectedKnowledgeIds.length > 0) {
            const selectedOnes = knowledgeItems.filter(knowledge =>
              knowledge.id !== undefined && selectedKnowledgeIds.includes(knowledge.id)
            );
            setSelectedKnowledge(selectedOnes);
          }
        } catch (error) {
          console.error('加载知识条目失败:', error);
        }
      }
    };

    loadKnowledge();
  };

  // 关闭知识库模态窗口
  const closeKnowledgeModal = () => {
    setShowKnowledgeModal(false);
  };

  // 移除选中的知识条目
  const handleRemoveKnowledge = (knowledgeId: number) => {
    // 从选中ID列表中移除
    const updatedIds = selectedKnowledgeIds.filter((id: number) => id !== knowledgeId); // 为id添加类型
    updateTypeMemory({ selectedKnowledgeIds: updatedIds });

    // 从选中知识条目数组中移除
    const updatedKnowledge = selectedKnowledge.filter((a: Knowledge) => a.id !== knowledgeId);
    setSelectedKnowledge(updatedKnowledge);
  };

  // 角色卡相关处理函数
  // 打开角色卡模态窗口
  const openCharacterModal = () => {
    setShowCharacterModal(true);

    // 加载当前作品的角色
    const loadCharacters = () => {
      if (workId) {
        try {
          const storageKey = `character_cards_${workId}`;
          const stored = localStorage.getItem(storageKey);
          if (stored) {
            const parsedCharacters = JSON.parse(stored).map((char: any) => ({
              ...char,
              createdAt: new Date(char.createdAt),
              updatedAt: new Date(char.updatedAt)
            }));
            setAvailableCharacters(parsedCharacters);

            // 更新已选择的角色
            if (selectedCharacterIds.length > 0) {
              const selectedOnes = parsedCharacters.filter((character: Character) =>
                selectedCharacterIds.includes(character.id)
              );
              setSelectedCharacters(selectedOnes);
            }
          }
        } catch (error) {
          console.error('加载角色数据失败:', error);
        }
      }
    };

    loadCharacters();
  };

  // 关闭角色卡模态窗口
  const closeCharacterModal = () => {
    setShowCharacterModal(false);
  };

  // 选择角色
  const handleCharacterSelect = (character: Character) => {
    const characterId = character.id;
    const currentSelectedIds = [...selectedCharacterIds];
    const index = currentSelectedIds.indexOf(characterId);

    let updatedSelectedIds: string[];
    let updatedSelectedCharacters: Character[];

    if (index === -1) {
      // 添加
      updatedSelectedIds = [...currentSelectedIds, characterId];
      updatedSelectedCharacters = [...selectedCharacters, character];
    } else {
      // 移除
      updatedSelectedIds = currentSelectedIds.filter(id => id !== characterId);
      updatedSelectedCharacters = selectedCharacters.filter(char => char.id !== characterId);
    }

    // 更新状态
    updateTypeMemory({ selectedCharacterIds: updatedSelectedIds });
    setSelectedCharacters(updatedSelectedCharacters);
  };

  // 移除选中的角色
  const handleRemoveCharacter = (characterId: string) => {
    // 从选中ID列表中移除
    const updatedIds = selectedCharacterIds.filter(id => id !== characterId);
    updateTypeMemory({ selectedCharacterIds: updatedIds });

    // 从选中角色数组中移除
    const updatedCharacters = selectedCharacters.filter(char => char.id !== characterId);
    setSelectedCharacters(updatedCharacters);
  };

  // 打开提示词选择模态窗口
  const openPromptSelectionModal = () => {
    setShowPromptSelectionModal(true);
  };

  // 关闭提示词选择模态窗口
  const closePromptSelectionModal = () => {
    setShowPromptSelectionModal(false);
  };

  // 处理提示词选择
  const handlePromptSelect = (prompt: UserPrompt) => {
    updateTypeMemory({ selectedPrompt: prompt });
    closePromptSelectionModal();
  };

  // 初始化类型和用户输入
  useEffect(() => {
    if (isOpen) {
      // 如果有初始类型且是润色（从选中文本进入），则使用润色但不保存到localStorage
      if (initialPromptType === 'ai_polishing' && currentContent && currentContent.trim() !== '') {
        // 设置类型但不保存到localStorage
        handleFunctionTypeChange('ai_polishing', false);
      }
      // 否则，已经在useState初始化时从localStorage读取了上次的选择
    }
  }, [isOpen, initialPromptType, currentContent]);

  // 当窗口打开或当前内容变化时，设置用户输入
  useEffect(() => {
    if (isOpen && currentContent && currentContent.trim() !== '') {
      // 如果是润色模式或初始类型是润色，则设置用户输入
      if (promptType === 'ai_polishing' || initialPromptType === 'ai_polishing') {
        console.log('设置用户输入:', currentContent);
        updateTypeMemory({ userInput: currentContent });
      }
    }
  }, [isOpen, currentContent, promptType]);

  // 加载用户提示词
  const loadUserPrompts = async (type: string) => {
    try {
      const response = await fetch('/api/prompt', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          category: 'userprompt',
          type: type,
          limit: 50
        })
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          return data.data || [];
        }
      }
      return [];
    } catch (error) {
      console.error('加载用户提示词失败:', error);
      return [];
    }
  };

  // 加载提示词和初始化可编辑内容
  useEffect(() => {
    if (isOpen) {
      // 加载提示词
      const loadPrompts = async () => {
        try {
          // 只加载用户提示词
          const userPromptsData = await loadUserPrompts(promptType);
          setUserPrompts(userPromptsData);

          // 恢复提示词选择状态
          const allPrompts = userPromptsData;
          const tempSelectedPrompt = (window as any).tempSelectedPrompt;

          if (tempSelectedPrompt) {
            // 查找保存的提示词
            const foundPrompt = allPrompts.find(p => String(p.id) === String(tempSelectedPrompt.id));
            if (foundPrompt) {
              updateTypeMemory({ selectedPrompt: foundPrompt });
            } else if (allPrompts.length > 0) {
              // 如果保存的提示词不存在，选择第一个
              updateTypeMemory({ selectedPrompt: allPrompts[0] });
            }
            // 清除临时存储
            delete (window as any).tempSelectedPrompt;
          } else if (allPrompts.length > 0 && !typeMemory[promptType].selectedPrompt) {
            // 如果没有保存的提示词且当前没有选择，默认选择第一个
            updateTypeMemory({ selectedPrompt: allPrompts[0] });
          }
        } catch (error) {
          console.error('加载提示词失败:', error);
          setError('加载提示词失败');
        }
      };

      // 从 localStorage 加载保存的章节关联和知识条目关联状态
      try {
        const savedMemory = localStorage.getItem(`ai_assistant_${promptType}_memory`);
        if (savedMemory) {
          const parsedMemory = JSON.parse(savedMemory);

          // 更新章节关联状态
          if (parsedMemory.selectedChapters && Array.isArray(parsedMemory.selectedChapters)) {
            // 验证章节索引是否有效
            const validChapters = parsedMemory.selectedChapters.filter(
              (index: number) => index >= 0 && index < chapters.length
            );
            updateTypeMemory({ selectedChapters: validChapters });
          }

          // 更新知识条目关联状态（兼容旧的 selectedArchiveIds 字段）
          const knowledgeIds = parsedMemory.selectedKnowledgeIds || parsedMemory.selectedArchiveIds;
          if (knowledgeIds && Array.isArray(knowledgeIds)) {
            updateTypeMemory({ selectedKnowledgeIds: knowledgeIds });
          }

          // 更新角色关联状态
          if (parsedMemory.selectedCharacterIds && Array.isArray(parsedMemory.selectedCharacterIds)) {
            updateTypeMemory({ selectedCharacterIds: parsedMemory.selectedCharacterIds });
          }

          // 更新自动关联状态
          if (typeof parsedMemory.isAutoAssociate === 'boolean') {
            setIsAutoAssociate(parsedMemory.isAutoAssociate);
          }

          // 更新自动关联计数
          if (typeof parsedMemory.autoAssociateCount === 'number') {
            setAutoAssociateCount(parsedMemory.autoAssociateCount);
          }

          // 更新提示词选择状态 - 延迟到提示词加载完成后处理
          if (parsedMemory.selectedPrompt) {
            // 暂存提示词信息，等待提示词加载完成后恢复
            (window as any).tempSelectedPrompt = parsedMemory.selectedPrompt;
          }
        }
      } catch (error) {
        console.error('加载章节关联状态失败:', error);
      }

      loadPrompts();
    }
  }, [isOpen, promptType, chapters.length]);

  // 监听生成内容的变化，自动滚动到最新内容
  useEffect(() => {
    if (generatedContent && showGenerationView) {
      // 使用scrollContainerRef直接控制滚动容器
      if (scrollContainerRef.current) {
        // 设置滚动到底部，使用平滑滚动
        scrollContainerRef.current.scrollTo({
          top: scrollContainerRef.current.scrollHeight,
          behavior: 'smooth'
        });
      }
    }
  }, [generatedContent, showGenerationView]);

  // 监听生成内容变化，计算字数
  useEffect(() => {
    const count = generatedContent ? generatedContent.trim().length : 0;
    setWordCount(count);
  }, [generatedContent]);

  // 生成内容
  const handleGenerate = async () => {
    // 检查按钮冷却状态，防止连续点击
    if (isButtonCooldown) {
      return;
    }

    // 设置按钮冷却状态，3秒内无法再次点击
    setIsButtonCooldown(true);
    setTimeout(() => {
      setIsButtonCooldown(false);
    }, 3000);

    // 检查提示词
    if (!isCustomPromptMode && !selectedPrompt) {
      setError('请选择一个提示词');
      return;
    }

    if (isCustomPromptMode && !customPromptContent.trim()) {
      setError('请输入自定义提示词');
      return;
    }

    // 检查用户余额
    try {
      const user = await getCurrentUser();
      console.log('获取到的用户信息:', user ? { id: user.id, email: user.email } : '无用户信息');

      if (user?.id) {
        console.log('开始检查用户余额...');
        const canUseResult = await BillingService.canUseAI(user.id, 100, selectedModel); // 预估100字消耗，传递模型代号
        console.log('余额检查结果:', canUseResult);

        if (!canUseResult.canUse) {
          setError('余额不足，请充值后再试');
          return;
        }
        console.log('余额检查通过，可以使用AI功能');
      } else {
        console.warn('未获取到用户信息，跳过余额检查');
        setError('未获取到用户信息，请重新登录');
        return;
      }
    } catch (balanceError) {
      console.error('余额检查失败:', balanceError);
      setError('余额检查失败，请重试');
      return;
    }

    setIsGenerating(true);
    setError('');
    setGeneratedContent('');
    setUsage(null); // 重置usage状态
    setConsumedWords(0); // 重置消耗字数状态
    setShowGenerationView(true); // 切换到生成视图
    // 移除了速度相关状态的重置

    try {
      // 构建消息 - 纯净格式
      // 系统提示词是提示词选项的内容，用户提示词根据类型决定
      let userContent = 'none';

      // 处理角色内容
      let characterContent = '';
      if (selectedCharacters.length > 0) {
        // 构建角色内容
        characterContent = '<关联角色>\n';
        selectedCharacters.forEach((character, index) => {
          let charInfo = `<角色${index + 1}>姓名：${character.name}`;
          if (character.gender !== '无') {
            charInfo += `，性别：${character.gender}`;
          }
          if (character.personality) {
            charInfo += `，性格：${character.personality}`;
          }
          if (character.background) {
            charInfo += `，背景：${character.background}`;
          }
          charInfo += `</角色${index + 1}>\n`;
          characterContent += charInfo;
        });
        characterContent += '</关联角色>';
      }

      // 处理知识内容
      let knowledgeContent = '';
      if (selectedKnowledge.length > 0) {
        // 构建知识内容
        knowledgeContent = '<关联知识>\n';
        selectedKnowledge.forEach((knowledge, index) => {
          knowledgeContent += `<知识${index + 1}>${knowledge.title}和${knowledge.content}</知识${index + 1}>\n`;
        });
        knowledgeContent += '</关联知识>';
      }

      // 如果有选中的章节，则添加章节内容
      let chaptersContent = '';
      if (selectedChapters.length > 0 && chapters.length > 0) {
        // 根据排序状态排序
        const sortedChapters = [...selectedChapters].sort((a, b) => isDescending ? b - a : a - b);

        // 构建章节内容
        chaptersContent = '<关联章节>\n';
        sortedChapters.forEach(index => {
          if (index >= 0 && index < chapters.length) {
            const chapter = chapters[index];
            chaptersContent += `<章节${index + 1}>${chapter.title || `第 ${index + 1} 章`}和${chapter.content}<章节${index + 1}>\n`;
          }
        });
        chaptersContent += '</关联章节>';
      }

      // 构建用户提示词内容
      let userPromptContent = '';
      if (userInput && userInput.trim() !== '') {
        userPromptContent = `<用户指令>${userInput}</用户指令>`;
      }

      // 构建系统提示词内容
      let systemPromptContent: string;
      if (isCustomPromptMode) {
        // 自定义提示词：直接使用用户输入的内容
        systemPromptContent = customPromptContent.trim();
      } else if (selectedPrompt) {
        // 只有用户提示词：发送ID，通用规则在后端stream中添加
        systemPromptContent = `__USER_PROMPT_ID__:${selectedPrompt.id}`;
      } else {
        // 这种情况不应该发生，因为前面已经检查过了
        setError('未找到有效的提示词');
        return;
      }

      // 组合所有内容
      userContent = '';
      if (userPromptContent) {
        userContent += userPromptContent + '\n\n';
      }
      if (chaptersContent) {
        userContent += chaptersContent + '\n\n';
      }
      if (characterContent) {
        userContent += characterContent + '\n\n';
      }
      if (knowledgeContent) {
        userContent += knowledgeContent;
      }

      // 如果没有任何内容，设置为默认值
      if (!userContent.trim()) {
        userContent = 'none';
      }

      // 记录提示词类型（用于调试）
      if (isCustomPromptMode) {
        console.log('提示词类型: 自定义提示词');
      } else if (selectedPrompt) {
        const isUserPrompt = userPrompts.some(p => String(p.id) === String(selectedPrompt.id));
        console.log('提示词类型:', isUserPrompt ? '用户提示词' : '未知提示词', selectedPrompt.id);
      }

      // 构建消息，但不立即解密提示词
      const messages: Message[] = [
        {
          role: 'system',
          // 使用新的系统提示词格式
          content: systemPromptContent
        },
        { role: 'user', content: userContent }
      ];

      // 流式生成处理 - 后端已经逐字符发送，前端直接显示
      let currentLength = 0; // 当前生成的内容长度

      // 开始生成
      console.log('>>> Calling generateAIContentStream...'); // Log before stream call
      await generateAIContentStream(
        messages,
        { model: selectedModel },
        (chunk) => {
          // 后端已经逐字符发送，前端直接显示
          if (!chunk) return;

          // 直接追加字符到生成内容
          setGeneratedContent(prev => prev + chunk);
          currentLength += chunk.length;
        },
        (usageData) => {
          // 处理usage回调
          console.log('收到usage数据:', usageData);
          setUsage(usageData);

          // 计算消耗字数
          if (usageData) {
            const promptTokens = usageData.prompt_tokens || 0;
            const totalTokens = usageData.total_tokens || 0;
            const outputTokens = totalTokens - promptTokens;
            const totalCost = BillingService.calculateTokenCost(promptTokens, totalTokens, selectedModel);
            setConsumedWords(totalCost);
            console.log(`计算消耗字数: 输入${promptTokens}token, 输出${outputTokens}token(总${totalTokens}-输入${promptTokens}), 总消耗${totalCost}字`);
          }
        }
      );
      // 移除了流结束的日志
    } catch (error: any) {
      console.error('生成内容失败:', error);
      // 在生成内容区域显示错误代码
      const errorCode = error.message || '500';
      setGeneratedContent(`错误：${errorCode}`);
    } finally {
      // 移除了 finally 块中的速度计算逻辑
      setIsGenerating(false);
    }
  };

  // 处理功能类型切换
  const handleFunctionTypeChange = async (type: 'ai_writing' | 'ai_polishing', saveToStorage: boolean = true) => {
    setPromptType(type);

    // 只有在明确指定需要保存到存储时才保存
    if (saveToStorage && typeof window !== 'undefined') {
      localStorage.setItem(LAST_USED_AI_FUNCTION_KEY, type);
    }

    // 重新加载对应类型的提示词
    try {
      const userPromptsData = await loadUserPrompts(type);
      setUserPrompts(userPromptsData);

      // 如果当前类型没有选择提示词，且有可用提示词，则选择第一个
      const allPrompts = userPromptsData;
      if (!typeMemory[type].selectedPrompt && allPrompts.length > 0) {
        updateTypeMemory({ selectedPrompt: allPrompts[0] });
      }
    } catch (error) {
      console.error('切换类型时加载提示词失败:', error);
    }
  };

  // 渲染选择视图
  const renderSelectionView = () => (
    <div className="content-container">
      {/* AI功能类型选择按钮组 */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
        <button
          onClick={() => handleFunctionTypeChange('ai_writing', true)}
          className={`relative py-5 px-4 rounded-xl flex flex-col items-center justify-center transition-all duration-300 ${
            promptType === 'ai_writing'
            ? 'bg-white shadow-md transform scale-105'
            : 'bg-white bg-opacity-70 hover:bg-white hover:shadow-sm'
          }`}
        >
          {/* Material Icons 图标 */}
          <div className="mb-3 w-12 h-12 flex items-center justify-center">
            <span className="material-icons text-[#5a9d6b]" style={{ fontSize: '2.5rem' }}>edit_note</span>
          </div>
          <span className="font-normal text-text-dark text-base" style={{fontFamily: "'Noto Sans SC', sans-serif"}}>AI写作</span>
          {promptType === 'ai_writing' && (
            <div className="absolute bottom-0 left-0 right-0 h-1 bg-[#5a9d6b] rounded-b-xl"></div>
          )}
        </button>

        <button
          onClick={() => handleFunctionTypeChange('ai_polishing', true)}
          className={`relative py-5 px-4 rounded-xl flex flex-col items-center justify-center transition-all duration-300 ${
            promptType === 'ai_polishing'
            ? 'bg-white shadow-md transform scale-105'
            : 'bg-white bg-opacity-70 hover:bg-white hover:shadow-sm'
          }`}
        >
          {/* Material Icons 图标 */}
          <div className="mb-3 w-12 h-12 flex items-center justify-center">
            <span className="material-icons text-[#7D85CC]" style={{ fontSize: '2.5rem' }}>auto_fix_high</span>
          </div>
          <span className="font-normal text-text-dark text-base" style={{fontFamily: "'Noto Sans SC', sans-serif"}}>AI润色</span>
          {promptType === 'ai_polishing' && (
            <div className="absolute bottom-0 left-0 right-0 h-1 bg-[#7D85CC] rounded-b-xl"></div>
          )}
        </button>


      </div>

      {/* 选项卡片分组 */}
      <div className="flex flex-col gap-4">
        {/* 模型选择卡片 */}
        <div className="relative bg-white px-4 py-3 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(120,180,140,0.15)]">
          <div className="flex items-center">
            <div className="flex items-center mr-3 min-w-[120px]">
              <div className="w-7 h-7 flex items-center justify-center mr-2">
                <span className="material-icons text-[#5a9d6b]" style={{ fontSize: '1.5rem' }}>model_training</span>
              </div>
              <h3 className="text-text-dark font-normal text-base" style={{fontFamily: "'Noto Sans SC', sans-serif"}}>选择模型</h3>
            </div>
            <div className="relative flex-1">
            <select
              value={selectedModel}
              onChange={(e) => updateGlobalSelectedModel(e.target.value)}
                className="w-full px-3 py-2 rounded-lg border border-[rgba(120,180,140,0.3)] bg-white focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent transition-all duration-200 appearance-none"
            >
              <option value={MODELS.LLM_TEST}>测试版（免费）</option>
              <option value={MODELS.LLM_CLAUDE}>克劳德（5X消耗）</option>
            </select>
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M6 9L12 15L18 9" stroke="#5a9d6b" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
          </div>
            </div>
          </div>
        </div>

        {/* 提示词选择卡片 */}
        <div className="relative bg-white px-4 py-3 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(120,180,140,0.15)]">
          <div className="flex items-center">
            <div className="flex items-center mr-3 min-w-[120px]">
              <div className="w-7 h-7 flex items-center justify-center mr-2">
                <span className="material-icons text-[#9C6FE0]" style={{ fontSize: '1.5rem' }}>lightbulb</span>
              </div>
              <h3 className="text-text-dark font-normal text-base" style={{fontFamily: "'Noto Sans SC', sans-serif"}}>
                {isCustomPromptMode ? '自定义提示词' : '选择提示词'}
              </h3>
            </div>
            <div className="relative flex-1 flex">
              {!isCustomPromptMode ? (
                // 选择模式
                <>
                  <div className="flex-1 relative">
                    <select
                      value={selectedPrompt?.id || ''}
                      onChange={(e) => {
                        const promptId = e.target.value;
                        // 只在用户提示词中查找
                        const prompt = userPrompts.find(p => String(p.id) === promptId) || null;
                        updateTypeMemory({ selectedPrompt: prompt });
                      }}
                      className="w-full px-3 py-2 rounded-lg border border-[rgba(120,180,140,0.3)] bg-white focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent transition-all duration-200 appearance-none"
                    >
                      {userPrompts.length === 0 && (
                        <option value="" disabled>无可用提示词</option>
                      )}
                      {/* 用户提示词 */}
                      {userPrompts.length > 0 && (
                        <optgroup label="📋 用户提示词">
                          {userPrompts.map(prompt => (
                            <option key={prompt.id} value={String(prompt.id)}>
                              {prompt.title}
                            </option>
                          ))}
                        </optgroup>
                      )}
                    </select>
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M6 9L12 15L18 9" stroke="#9C6FE0" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </div>
                  </div>
                  <button
                    onClick={() => setIsCustomPromptMode(true)}
                    className="ml-2 px-3 py-2 bg-[rgba(156,111,224,0.1)] text-[#9C6FE0] rounded-lg hover:bg-[rgba(156,111,224,0.2)] transition-all flex items-center"
                  >
                    <span className="material-icons text-sm mr-1">edit</span>
                    自定义
                  </button>
                  <button
                    onClick={openPromptSelectionModal}
                    className="ml-2 px-3 py-2 bg-[rgba(109,92,77,0.1)] text-[#6d5c4d] rounded-lg hover:bg-[rgba(109,92,77,0.2)] transition-all flex items-center"
                  >
                    <span className="material-icons text-sm mr-1">inventory_2</span>
                    仓库
                  </button>
                </>
              ) : (
                // 自定义模式
                <>
                  <div className="flex-1 relative">
                    <textarea
                      value={customPromptContent}
                      onChange={(e) => setCustomPromptContent(e.target.value)}
                      className="w-full px-3 py-2 rounded-lg border border-[rgba(120,180,140,0.3)] bg-white focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent transition-all duration-200 min-h-[80px] resize-none"
                      placeholder="请输入您的自定义提示词..."
                    />
                  </div>
                  <button
                    onClick={() => setIsCustomPromptMode(false)}
                    className="ml-2 px-3 py-2 bg-[rgba(109,92,77,0.1)] text-[#6d5c4d] rounded-lg hover:bg-[rgba(109,92,77,0.2)] transition-all flex items-center"
                  >
                    <span className="material-icons text-sm mr-1">arrow_back</span>
                    返回选择
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* 用户输入框 */}
      <div className="relative bg-white p-5 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(120,180,140,0.15)]">
        <div className="flex flex-col">
          <div className="flex items-center mb-3.5">
            <div className="w-8 h-8 flex items-center justify-center mr-2.5">
              <span className="material-icons text-[#E0C56F]" style={{ fontSize: '1.75rem' }}>chat</span>
            </div>
            <h3 className="text-text-dark font-normal text-base" style={{fontFamily: "'Noto Sans SC', sans-serif"}}>用户输入</h3>
          </div>
          <textarea
            value={userInput}
            onChange={(e) => updateTypeMemory({ userInput: e.target.value })}
            className="w-full p-4 rounded-lg border border-[rgba(120,180,140,0.3)] bg-white focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent transition-all duration-200 min-h-[120px] text-text-medium resize-none"
            placeholder="在此输入您的具体要求，补充或覆盖系统提示词的部分内容..."
          />
        </div>
      </div>

      {/* 关联知识和角色卡片区域 - 水平布局 */}
      {workId !== undefined && ( // 仅在有workId时显示关联区域
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          {/* 关联知识区域 */}
          <div className="bg-white p-5 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(120,180,140,0.15)]">
            <div className="flex justify-between items-center mb-3.5">
              <div className="flex items-center">
                <div className="w-8 h-8 flex items-center justify-center mr-2.5">
                  <span className="material-icons text-[#7D85CC]" style={{ fontSize: '1.75rem' }}>folder_special</span>
                </div>
                <h3 className="text-text-dark font-normal text-base" style={{fontFamily: "'Noto Sans SC', sans-serif"}}>关联知识</h3>
              </div>
              <button
                onClick={openKnowledgeModal}
                className="px-3 py-1.5 bg-gradient-to-br from-[#7D85CC] to-[#6b73b3] text-white text-sm rounded-lg flex items-center hover:shadow-md transition-all"
              >
                <svg className="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 5V19" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M5 12H19" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span className="hidden sm:inline">选择知识条目</span>
                <span className="sm:hidden">选择知识</span>
              </button>
            </div>

            {selectedKnowledge.length > 0 ? (
              <div className="space-y-2.5 max-h-[120px] overflow-y-auto pr-1 custom-scrollbar">
                {selectedKnowledge.map(knowledge => (
                  <div key={knowledge.id} className="flex justify-between items-center p-2.5 rounded-lg bg-[rgba(125,133,204,0.08)] border border-[rgba(125,133,204,0.15)] hover:shadow-sm transition-all">
                    <div className="flex items-center flex-1 min-w-0">
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-text-dark truncate text-sm">
                          {knowledge.title}
                        </div>
                        <div className="text-xs text-text-medium truncate">
                          {knowledge.content.substring(0, 40)}
                          {knowledge.content.length > 40 && '...'}
                        </div>
                      </div>
                    </div>
                    <span
                      className="cursor-pointer hover:text-red-500 ml-2"
                      onClick={() => handleRemoveKnowledge(knowledge.id as number)}
                    >
                      <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M18 6L6 18" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M6 6L18 18" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </span>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-sm text-text-medium italic bg-[#f7f8fc] p-3 rounded-lg border border-[#7D85CC]/10">
                <div className="flex items-center">
                  <svg className="w-5 h-5 mr-2 text-[#7D85CC]" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="12" cy="12" r="9" stroke="#7D85CC" strokeWidth="1.5"/>
                    <path d="M12 8V12.5" stroke="#7D85CC" strokeWidth="1.5" strokeLinecap="round"/>
                    <circle cx="12" cy="16" r="1" fill="#7D85CC"/>
                  </svg>
                  <span className="text-xs">点击"选择知识"按钮关联相关知识条目。</span>
                </div>
              </div>
            )}
          </div>

          {/* 角色卡片区域 */}
          <div className="bg-white p-5 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(120,180,140,0.15)]">
            <div className="flex justify-between items-center mb-3.5">
              <div className="flex items-center">
                <div className="w-8 h-8 flex items-center justify-center mr-2.5">
                  <span className="material-icons text-[#9c6fe0]" style={{ fontSize: '1.75rem' }}>person</span>
                </div>
                <h3 className="text-text-dark font-normal text-base" style={{fontFamily: "'Noto Sans SC', sans-serif"}}>角色卡片</h3>
              </div>
              <button
                onClick={openCharacterModal}
                className="px-3 py-1.5 bg-gradient-to-br from-[#9c6fe0] to-[#8c5fd0] text-white text-sm rounded-lg flex items-center hover:shadow-md transition-all"
              >
                <svg className="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 5V19" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M5 12H19" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span className="hidden sm:inline">选择角色</span>
                <span className="sm:hidden">选择角色</span>
              </button>
            </div>

            {selectedCharacters.length > 0 ? (
              <div className="space-y-2.5 max-h-[120px] overflow-y-auto pr-1 custom-scrollbar">
                {selectedCharacters.map(character => (
                  <div key={character.id} className="flex justify-between items-center p-2.5 rounded-lg bg-[rgba(156,111,224,0.08)] border border-[rgba(156,111,224,0.15)] hover:shadow-sm transition-all">
                    <div className="flex items-center flex-1 min-w-0">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center mb-1">
                          <div className="font-medium text-text-dark truncate mr-2 text-sm">
                            {character.name}
                          </div>
                          {character.gender !== '无' && (
                            <span className="inline-block bg-gray-100 px-1.5 py-0.5 rounded text-xs text-gray-600">
                              {character.gender}
                            </span>
                          )}
                        </div>
                        {character.personality && (
                          <div className="text-xs text-text-medium truncate">
                            {character.personality.substring(0, 25)}
                            {character.personality.length > 25 && '...'}
                          </div>
                        )}
                      </div>
                    </div>
                    <span
                      className="cursor-pointer hover:text-red-500 ml-2"
                      onClick={() => handleRemoveCharacter(character.id)}
                    >
                      <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M18 6L6 18" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M6 6L18 18" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </span>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-sm text-text-medium italic bg-[#f9f7ff] p-3 rounded-lg border border-[#9c6fe0]/10">
                <div className="flex items-center">
                  <svg className="w-5 h-5 mr-2 text-[#9c6fe0]" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="12" cy="12" r="9" stroke="#9c6fe0" strokeWidth="1.5"/>
                    <path d="M12 8V12.5" stroke="#9c6fe0" strokeWidth="1.5" strokeLinecap="round"/>
                    <circle cx="12" cy="16" r="1" fill="#9c6fe0"/>
                  </svg>
                  <span className="text-xs">点击"选择角色"按钮关联相关角色。</span>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 章节关联模块 */}
      {chapters.length > 0 && (
        <div className="relative bg-white p-5 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(120,180,140,0.15)]">
          <div className="flex flex-col">
            <div className="flex justify-between items-center mb-3.5">
              <div className="flex items-center">
                <div className="w-8 h-8 flex items-center justify-center mr-2.5">
                  <span className="material-icons text-[#E0976F]" style={{ fontSize: '1.75rem' }}>menu_book</span>
                </div>
                <h3 className="text-text-dark font-normal text-base" style={{fontFamily: "'Noto Sans SC', sans-serif"}}>章节关联</h3>
              </div>
              <button
                type="button"
                onClick={openChapterModal}
                className="px-3.5 py-1.5 bg-gradient-to-br from-[#E0976F] to-[#e08a58] text-white text-sm rounded-lg flex items-center hover:shadow-md transition-all"
              >
                <svg className="w-4 h-4 mr-1.5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 5V19" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M5 12H19" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                选择章节
              </button>
            </div>



            {selectedChapters.length > 0 ? (
              <div>
                <div className="text-sm text-text-medium mb-2.5">
                  已选择 {selectedChapters.length} 个章节：
                </div>
                <div className="flex flex-wrap gap-2">
                  {selectedChapters.sort((a, b) => a - b).map(index => (
                    <div
                      key={index}
                      className="flex items-center bg-[#faf0e6] border border-[#E0976F]/30 rounded-lg px-2.5 py-1.5 text-sm text-[#E0976F] transition-all duration-200 hover:shadow-sm"
                    >
                      <svg className="w-3.5 h-3.5 mr-1.5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <rect x="4" y="4" width="16" height="16" rx="2" stroke="#E0976F" strokeWidth="1.5"/>
                        <path d="M9 12H15" stroke="#E0976F" strokeWidth="1.5" strokeLinecap="round"/>
                        <path d="M12 9L12 15" stroke="#E0976F" strokeWidth="1.5" strokeLinecap="round"/>
                      </svg>
                      <span className="mr-1.5">第 {index + 1} 章</span>
                      <span className="cursor-pointer hover:text-red-500" onClick={() => handleChapterSelect(index)}>
                        <svg className="w-3.5 h-3.5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M18 6L6 18" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M6 6L18 18" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="text-sm text-text-medium italic bg-[#faf7f2] p-3 rounded-lg border border-[#E0976F]/10">
                <div className="flex items-center">
                  <svg className="w-5 h-5 mr-2 text-[#E0976F]" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="12" cy="12" r="9" stroke="#E0976F" strokeWidth="1.5"/>
                    <path d="M12 8V12.5" stroke="#E0976F" strokeWidth="1.5" strokeLinecap="round"/>
                    <circle cx="12" cy="16" r="1" fill="#E0976F"/>
                  </svg>
                  未选择任何章节，AI将无法获取作品的其他章节内容。
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );

  // 当用户从生成视图返回选项视图时设置标志
  const handleReturnToSelection = () => {
    setShowGenerationView(false);
    setHasReturnedFromGeneration(true);
  };

  // 渲染生成视图
  const renderGenerationView = () => {
    // 移除了渲染速度的日志
    return (
      // Return the content directly, removing the white page container
      <>
      {/* 顶部生成中或完成状态指示器 */}
      {/* 顶部状态指示器 - Other Results Logic */}
      {(isGenerating || wordCount > 0) && ( // 仅在处理中或有内容时显示
        <div className="absolute top-4 left-1/2 transform -translate-x-1/2 z-10 bg-white shadow-md rounded-full px-4 py-1.5 text-sm flex items-center border border-[rgba(120,180,140,0.3)]">
          {(isGenerating && wordCount > 0) || (!isGenerating && wordCount > 0) ? ( // 处理中或处理完成，且有内容
            <>
              <svg className="w-5 h-5 mr-2 fill-current text-primary-green" viewBox="0 0 24 24">
                <path d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z" />
              </svg>
              <span className="text-primary-green font-medium">已生成 {wordCount} 字</span>
            </>
          ) : null}
        </div>
      )}

      {/* 错误信息 */}
      {error && (
        <div className="relative bg-white p-3 rounded-xl shadow-sm border border-red-200 mb-3 mt-10">
          <div className="flex items-center text-red-600">
            <span className="material-icons mr-2">error_outline</span>
            <p className="text-text-dark font-medium text-sm">{error}</p>
          </div>
        </div>
      )}

      {/* 居中加载状态 */}
      {isGenerating && wordCount === 0 && (
        <div className="flex flex-col items-center justify-center h-64">
          <StarLogo size={48} />
          <span className="mt-4 text-primary-green font-medium">AI正在深度思考 {elapsedTime.toFixed(1)}秒</span>
        </div>
      )}

      {/* 生成结果 - 直接渲染文本内容 */}
      {(!isGenerating || wordCount > 0) && (
        <div
          ref={resultContainerRef}
          className="whitespace-pre-wrap text-text-dark text-[14pt] leading-relaxed font-normal mt-10" // 添加上边距避免与状态指示器重叠
          style={{fontFamily: "'Noto Sans SC', sans-serif"}}
        >
          {generatedContent}
        </div>
      )}
      </>
    );
  };

  // 关闭弹窗时重置状态
  const handleClose = () => {
    // 如果正在生成且生成未完成，不允许关闭
    if (isGenerating) return;

    // 重置状态 - 但保留章节关联和档案关联状态
    setHasReturnedFromGeneration(false);
    setGeneratedContent('');
    setShowGenerationView(false);
    setError('');
    setPreviewedChapterIndex(null); // 重置预览章节

    // 保存当前的章节关联和知识条目关联状态到localStorage
    try {
      const memoryToSave = {
        selectedPrompt: typeMemory[promptType].selectedPrompt,
        selectedChapters: typeMemory[promptType].selectedChapters,
        selectedKnowledgeIds: typeMemory[promptType].selectedKnowledgeIds,
        selectedCharacterIds: typeMemory[promptType].selectedCharacterIds,
        isAutoAssociate: isAutoAssociate,
        autoAssociateCount: autoAssociateCount
      };
      localStorage.setItem(`ai_assistant_${promptType}_memory`, JSON.stringify(memoryToSave));
    } catch (error) {
      console.error('保存章节关联状态失败:', error);
    }

    // 如果是从选中润色进入的临时模式，关闭时不要影响localStorage中保存的上次选择
    // 通过检查是否有选中文本且初始类型是润色来判断
    if (initialPromptType === 'ai_polishing' && currentContent && currentContent.trim() !== '') {
      // 不做任何操作，保持localStorage中的上次选择不变
    }

    // 调用外部关闭函数
    onClose();
  };


  // 渲染底部按钮
  const renderFooter = () => {
    // 选择视图的底部按钮
    if (!showGenerationView) {
      return (
        <div className="flex justify-between items-center pt-2">
          {/* 左侧：错误信息显示 */}
          <div className="flex-1 mr-4">
            {error && (
              <div className="flex items-center text-red-600 bg-red-50 px-3 py-2 rounded-lg border border-red-200">
                <span className="material-icons mr-2 text-sm">error_outline</span>
                <p className="text-red-600 font-medium text-sm">{error}</p>
              </div>
            )}
          </div>

          {/* 右侧：操作按钮 */}
          <div className="flex space-x-3">
            <button
              onClick={hasReturnedFromGeneration ? () => setShowGenerationView(true) : handleClose}
              className="ghibli-button outline text-sm py-2 transition-all duration-200 flex items-center"
            >
              <span className="material-icons mr-1 text-sm">
                arrow_back
              </span>
              {hasReturnedFromGeneration ? '返回结果' : '关闭'}
            </button>
          <button
            onClick={handleGenerate}
            disabled={isGenerating || (!isCustomPromptMode && !selectedPrompt) || (isCustomPromptMode && !customPromptContent.trim()) || isButtonCooldown || !!error}
            className={`relative overflow-hidden ghibli-button text-sm py-2 transition-all duration-300
              ${isGenerating || (!isCustomPromptMode && !selectedPrompt) || (isCustomPromptMode && !customPromptContent.trim()) || isButtonCooldown || !!error
                ? 'opacity-50 cursor-not-allowed'
                : 'hover:shadow-md'}`}
            title={error ? error : (
              !isCustomPromptMode && !selectedPrompt ? '请选择提示词' :
              isCustomPromptMode && !customPromptContent.trim() ? '请输入自定义提示词' : ''
            )}
          >
            {!isButtonCooldown && !isGenerating && (
              <svg className="absolute -right-12 -bottom-8 w-24 h-24 opacity-10" viewBox="0 0 24 24">
                <path d="M12 3C16.9706 3 21 7.02944 21 12H24L20 16L16 12H19C19 8.13401 15.866 5 12 5C8.13401 5 5 8.13401 5 12C5 15.866 8.13401 19 12 19C13.5719 19 15.0239 18.481 16.1922 17.6056L17.6568 19.0703C16.134 20.3001 14.16 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3Z" />
              </svg>
            )}
            <span className="material-icons mr-1 text-sm">psychology</span>
            {hasReturnedFromGeneration ? '重新生成' : '开始生成'}
          </button>
          </div>
        </div>
      );
    }

    // 生成视图的底部按钮
    return (
      <div className="flex justify-between items-center pt-2">
        {/* 左侧：消耗字数统计信息 */}
        <div className="flex items-center">
          {consumedWords > 0 && (
            <div className="text-sm text-text-medium bg-gray-50 px-3 py-1.5 rounded-lg border">
              <span className="material-icons text-sm mr-1 align-middle">analytics</span>
              已消耗 {consumedWords} 字
              {/* 根据模型类型显示标注 */}
              {selectedModel === MODELS.LLM_TEST &&
                <span className="text-text-medium ml-1">(免费)</span>
              }
              {selectedModel === MODELS.LLM_CLAUDE &&
                <span className="text-text-medium ml-1">(5X)</span>
              }
            </div>
          )}
        </div>

        {/* 右侧：操作按钮 */}
        <div className="flex space-x-3">
          <button
            onClick={handleReturnToSelection}
            disabled={isGenerating} // 生成过程中禁用返回按钮
            className={`ghibli-button outline text-sm py-2 transition-all duration-200 flex items-center
              ${isGenerating ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            <span className="material-icons mr-1 text-sm">arrow_back</span>
            返回选项
          </button>

          {generatedContent && !isGenerating && (
            <button
              onClick={() => {
                onInsertToEditor(generatedContent);
                handleClose();
              }}
              className="ghibli-button text-sm py-2 transition-all duration-300 hover:shadow-md flex items-center"
            >
              <span className="material-icons mr-1 text-sm">publish</span>
              应用到正文
            </button>
          )}

        </div>
      </div>
    );
  };

  // 移除了组件渲染日志

  return (
    <>
      <Modal
        isOpen={isOpen}
        onClose={handleClose}
        title={
          <div className="flex items-center">
            <div className="w-10 h-10 rounded-full bg-gradient-to-br from-[#5a9d6b] to-[#65ad79] flex items-center justify-center mr-3 text-white shadow-md">
              <span className="material-icons text-lg">psychology</span>
            </div>
            <span style={{fontFamily: "'Ma Shan Zheng', cursive"}} className="text-xl text-text-dark">
              AI创作助手
            </span>
          </div>
        }
        footer={renderFooter()}
        maxWidth="max-w-4xl sm:max-w-[95vw] md:max-w-3xl lg:max-w-4xl"
      >
        <div
          ref={scrollContainerRef}
          className="scrollable-container"
        >
          {showGenerationView ? renderGenerationView() : renderSelectionView()}
        </div>
      </Modal>

      {/* 章节选择模态窗口 */}
      {showChapterModal && (
        <ChapterAssociationModal
          isOpen={showChapterModal}
          onClose={closeChapterModal}
          onConfirm={confirmChapterSelection}
          chapters={chapters}
          initialSelectedChapters={selectedChapters}
          isDescending={isDescending}
          initialIsAutoAssociate={isAutoAssociate}
          initialAutoAssociateCount={autoAssociateCount}
        />
      )}

      {/* 知识库模态窗口 - 简化版，仅用于选择知识条目 */}
      {showKnowledgeModal && (
        <AIKnowledgeSelectionModal
          isOpen={showKnowledgeModal}
          onClose={closeKnowledgeModal}
          onSelect={handleKnowledgeSelect}
          workId={workId}
          initialSelectedIds={typeMemory[promptType].selectedKnowledgeIds}
        />
      )}

      {/* 角色卡选择模态窗口 - 简化版，仅用于选择角色 */}
      {showCharacterModal && (
        <AICharacterSelectionModal
          isOpen={showCharacterModal}
          onClose={closeCharacterModal}
          onSelect={handleCharacterSelect}
          workId={workId}
          initialSelectedIds={typeMemory[promptType].selectedCharacterIds}
        />
      )}



      {/* 提示词选择模态窗口 */}
      {showPromptSelectionModal && (
        <PromptSelectionModal
          isOpen={showPromptSelectionModal}
          onClose={closePromptSelectionModal}
          onSelect={handlePromptSelect}
          promptType={promptType}
          initialSelectedId={selectedPrompt?.id}
        />
      )}
    </>
  );
};