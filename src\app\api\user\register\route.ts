/**
 * 用户注册API路由
 * 处理用户注册
 */
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { handleSupabaseAuthError, isUserAlreadyRegisteredError } from '@/lib/utils/ErrorHandler';

// Supabase配置
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

// 创建Supabase客户端
const supabase = createClient(supabaseUrl, supabaseAnonKey);

/**
 * POST 方法 - 用户注册
 */
export async function POST(request: NextRequest) {
  try {
    // 解析请求体
    const body = await request.json();
    const { email, password, userId } = body;

    // 验证请求数据
    if (!email || !password || !userId) {
      return NextResponse.json(
        { success: false, message: '邮箱、密码和用户名都是必填项' },
        { status: 400 }
      );
    }



    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { success: false, message: '邮箱格式不正确' },
        { status: 400 }
      );
    }

    // 验证邮箱域名
    const allowedDomains = ['qq.com', '163.com', 'gmail.com'];
    const emailDomain = email.split('@')[1]?.toLowerCase();
    if (!emailDomain || !allowedDomains.includes(emailDomain)) {
      return NextResponse.json(
        { success: false, message: '只支持 @qq.com、@163.com 和 @gmail.com 邮箱注册' },
        { status: 400 }
      );
    }

    // 验证密码长度
    if (password.length < 6) {
      return NextResponse.json(
        { success: false, message: '密码长度至少为6个字符' },
        { status: 400 }
      );
    }

    // 验证用户名长度
    if (userId.trim().length === 0 || userId.length > 50) {
      return NextResponse.json(
        { success: false, message: '用户名不能为空且长度不能超过50个字符' },
        { status: 400 }
      );
    }

    console.log('开始创建用户:', email);

    // 使用Supabase标准注册API（OTP模式，无邮箱重定向）
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          name: userId,
          display_name: userId,
        }
        // 移除 emailRedirectTo，使用OTP验证模式
      }
    });

    if (error) {
      console.error('注册失败:', error);

      // 如果是用户已存在错误，检查验证状态
      if (isUserAlreadyRegisteredError(error)) {
        try {
          // 调用数据库函数检查验证状态
          const { data: statusResult, error: statusError } = await supabase.rpc('check_user_verification_status', {
            user_email: email
          });

          if (statusError) {
            console.error('检查用户状态失败:', statusError);
            return NextResponse.json(
              { success: false, message: handleSupabaseAuthError(error) },
              { status: 400 }
            );
          }

          // 如果用户存在但未验证，重发验证码
          if (statusResult?.exists && !statusResult?.verified) {
            const { error: resendError } = await supabase.auth.resend({
              type: 'signup',
              email: email
            });

            if (resendError) {
              console.error('重发验证码失败:', resendError);
              return NextResponse.json(
                { success: false, message: '重发验证码失败，请稍后重试' },
                { status: 400 }
              );
            }

            console.log('验证码已重新发送给未验证用户:', email);
            return NextResponse.json({
              success: true,
              message: '验证码已重新发送，请查收邮件',
              needsEmailConfirmation: true
            });
          }
        } catch (checkError) {
          console.error('检查用户状态异常:', checkError);
        }
      }

      // 其他错误或已验证用户，使用统一错误处理
      return NextResponse.json(
        { success: false, message: handleSupabaseAuthError(error) },
        { status: 400 }
      );
    }

    console.log('用户注册请求成功处理');

    // 返回成功响应
    return NextResponse.json({
      success: true,
      message: '注册成功，请验证您的邮箱',
      user: data.user,
      needsEmailConfirmation: !data.session // 如果没有session，说明需要邮箱确认
    });
  } catch (error) {
    console.error('注册API异常:', error);
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : '服务器内部错误'
      },
      { status: 500 }
    );
  }
}
