'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useAuth } from '@/hooks/useAuth';
import RechargeModal from '@/components/common/modals/RechargeModal';
import UserAccountModal from './UserAccountModal';
import { BillingService } from '@/lib/billing';

// 会员信息类型定义
interface MembershipInfo {
  is_active: boolean;
  level: string;
  days_remaining: number | null;
  word_limit: number | null;
  word_used: number;
  word_remaining: number | null;
}

/**
 * 用户账号按钮组件
 * 登录后显示用户账号信息
 */
export default function UserAccountButton() {
  const { user, signOut } = useAuth();
  const [showUserModal, setShowUserModal] = useState(false);
  const [showRechargeModal, setShowRechargeModal] = useState(false);
  const [membershipInfo, setMembershipInfo] = useState<MembershipInfo | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // 使用ref来跟踪窗口是否已经获取过数据
  const hasLoadedRef = useRef(false);
  // 使用ref来跟踪窗口是否已经打开
  const wasOpenRef = useRef(false);

  // 获取会员信息
  useEffect(() => {
    // 每次窗口打开时都重新获取数据（强制刷新）
    if (showUserModal && user) {
      // 标记窗口已经打开
      wasOpenRef.current = true;

      // 设置加载状态
      setIsLoading(true);

      // 重置缓存标记，强制重新获取数据
      hasLoadedRef.current = false;

      // 获取会员信息
      const fetchMembershipInfo = async () => {
        try {
          console.log('UserAccountButton: 开始获取会员信息，用户:', {
            id: user.id,
            email: user.email
          });

          // 使用BillingService查询membership-look表
          const memberData = await BillingService.getMembershipInfo(user.id);

          if (!memberData) {
            console.log('UserAccountButton: 用户不存在于membership-look表中，可能是新用户');
            // 前端不应该直接创建用户记录，这应该在用户注册时由后端处理
            // 这里只是显示一个默认的免费用户状态
            setMembershipInfo({
              is_active: false,
              level: '免费',
              days_remaining: null,
              word_limit: 0,
              word_used: 0,
              word_remaining: 0
            });
          } else {
            console.log('UserAccountButton: 成功获取到会员数据:', memberData);
            setMembershipInfo(memberData);
          }

          // 标记已经加载过数据
          hasLoadedRef.current = true;
        } catch (error) {
          console.error('UserAccountButton: 获取用户信息失败:', error);
          // 设置默认的免费用户状态以防止界面异常
          setMembershipInfo({
            is_active: false,
            level: '免费',
            days_remaining: null,
            word_limit: 0,
            word_used: 0,
            word_remaining: 0
          });
        } finally {
          setIsLoading(false);
        }
      };

      fetchMembershipInfo();
    }

    // 当窗口关闭时，重置窗口打开状态
    if (!showUserModal) {
      wasOpenRef.current = false;
    }
  }, [showUserModal, user, membershipInfo]);

  // 如果用户未登录，返回null
  if (!user) return null;

  // 用户ID和UID
  const userId = user.user_metadata?.display_name || user.user_metadata?.name || '未设置';
  const uid = user.id;

  // 格式化会员级别显示
  const formatMemberLevel = (level: string) => {
    switch (level) {
      case '免费': return '免费';
      case '普通会员': return '普通会员';
      case '高级会员': return '高级会员';
      case '黑金会员': return '黑金会员';
      default: return level || '免费';
    }
  };

  // 格式化数字显示（添加千位分隔符）
  const formatNumber = (num: number | null) => {
    if (num === null || num === undefined) return '0';
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  };

  // 格式化字数显示（使用中文万、亿单位）
  const formatWordCount = (count: number): string => {
    if (count < 10000) {
      return `${count}字`;
    }

    if (count < *********) { // 小于1亿
      const wan = count / 10000;
      if (wan >= 10) {
        return `${Math.floor(wan)}万字`; // 整数万
      } else {
        return `${wan.toFixed(1)}万字`; // 保留1位小数
      }
    }

    // 大于等于1亿
    const yi = count / *********;
    if (yi >= 10) {
      return `${Math.floor(yi)}亿字`; // 整数亿
    } else {
      return `${yi.toFixed(1)}亿字`; // 保留1位小数
    }
  };

  return (
    <div className="relative">
      {/* 用户账号按钮 */}
      <button
        className="flex items-center space-x-2 text-text-dark hover:text-primary-green transition-colors duration-200"
        onClick={() => setShowUserModal(true)}
      >
        <span className="material-icons text-xl">account_circle</span>
        <div className="flex flex-col items-start">
          <span className="text-sm">我的账号</span>
          {membershipInfo && (
            <span className="text-xs text-text-medium">
              {membershipInfo.level === '免费' ? (
                '免费用户 (0字额度)'
              ) : (
                `剩余 ${formatWordCount(membershipInfo.word_remaining || 0)}`
              )}
            </span>
          )}
        </div>
      </button>

      {/* 用户账号信息模态窗口 */}
      <UserAccountModal
        isOpen={showUserModal}
        onClose={() => setShowUserModal(false)}
        userId={userId}
        uid={uid}
        membershipInfo={membershipInfo}
        isLoading={isLoading}
        onRecharge={() => setShowRechargeModal(true)}
        onSignOut={signOut}
      />

      {/* 充值模态窗口 */}
      <RechargeModal
        isOpen={showRechargeModal}
        onClose={() => setShowRechargeModal(false)}
      />
    </div>
  );
}
