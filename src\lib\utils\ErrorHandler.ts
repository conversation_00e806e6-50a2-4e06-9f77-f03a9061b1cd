/**
 * Supabase错误处理工具
 * 统一处理Supabase返回的英文错误信息，转换为用户友好的中文提示
 */

/**
 * Supabase认证错误类型映射
 */
const SUPABASE_AUTH_ERROR_MAP: Record<string, string> = {
  // 注册相关错误
  'User already registered': '该邮箱已被注册',
  'user_already_exists': '该邮箱已被注册',
  'signup_disabled': '注册功能已禁用',
  'email_address_invalid': '邮箱地址格式不正确',
  'password_too_short': '密码长度不足',
  'weak_password': '密码强度不够，请使用更复杂的密码',
  
  // 登录相关错误
  'Invalid login credentials': '邮箱或密码错误',
  'invalid_credentials': '邮箱或密码错误',
  'Email not confirmed': '请先验证您的邮箱',
  'email_not_confirmed': '请先验证您的邮箱',
  'Too many requests': '请求过于频繁，请稍后再试',
  'too_many_requests': '请求过于频繁，请稍后再试',
  'rate_limit_exceeded': '请求过于频繁，请稍后再试',
  'account_locked': '账户已被锁定，请联系管理员',
  'user_not_found': '用户不存在',
  
  // OTP验证相关错误
  'expired': '验证码已过期，请重新获取',
  'token_expired': '验证码已过期，请重新获取',
  'Token has expired or is invalid': '验证码已过期或无效，请重新获取',
  'invalid': '验证码无效，请检查后重试',
  'invalid_token': '验证码无效，请检查后重试',
  'otp_expired': '验证码已过期，请重新获取',
  'otp_invalid': '验证码无效，请检查后重试',
  'too_many_otp_attempts': '验证尝试次数过多，请稍后再试',
  
  // 密码重置相关错误
  'password_reset_limit_exceeded': '密码重置请求过于频繁，请稍后再试',
  'same_password': '新密码不能与当前密码相同',
  'not_found': '该邮箱未注册，请检查邮箱地址',
  
  // 会话相关错误
  'session_expired': '会话已过期，请重新登录',
  'invalid_session': '会话无效，请重新登录',
  'refresh_token_expired': '登录已过期，请重新登录',
  
  // 网络和服务器错误
  'network_error': '网络连接失败，请检查网络后重试',
  'server_error': '服务器错误，请稍后重试',
  'service_unavailable': '服务暂时不可用，请稍后重试',
  'timeout': '请求超时，请重试',
  
  // 权限相关错误
  'unauthorized': '未授权访问',
  'forbidden': '访问被拒绝',
  'insufficient_permissions': '权限不足'
};

/**
 * 处理Supabase认证错误
 * @param error Supabase错误对象或错误消息
 * @returns 用户友好的中文错误消息
 */
export const handleSupabaseAuthError = (error: any): string => {
  if (!error) {
    return '未知错误，请重试';
  }

  // 获取错误消息
  const errorMessage = typeof error === 'string' ? error : (error.message || error.error_description || '');
  
  // 如果没有错误消息，返回默认错误
  if (!errorMessage) {
    return '您的操作太频繁了，请稍后再试';
  }

  // 遍历错误映射，查找匹配的错误类型
  for (const [englishError, chineseMessage] of Object.entries(SUPABASE_AUTH_ERROR_MAP)) {
    if (errorMessage.toLowerCase().includes(englishError.toLowerCase())) {
      return chineseMessage;
    }
  }

  // 如果没有找到匹配的错误类型，返回原始错误消息（如果已经是中文）或默认错误
  if (/[\u4e00-\u9fff]/.test(errorMessage)) {
    // 如果错误消息包含中文字符，直接返回
    return errorMessage;
  }

  // 返回默认错误消息
  return '您的操作太频繁了，请稍后再试';
};

/**
 * 检查是否为用户已注册错误
 * @param error 错误对象或消息
 * @returns 是否为用户已注册错误
 */
export const isUserAlreadyRegisteredError = (error: any): boolean => {
  const errorMessage = typeof error === 'string' ? error : (error?.message || '');
  return errorMessage.toLowerCase().includes('user already registered') || 
         errorMessage.toLowerCase().includes('user_already_exists');
};

/**
 * 检查是否为邮箱未确认错误
 * @param error 错误对象或消息
 * @returns 是否为邮箱未确认错误
 */
export const isEmailNotConfirmedError = (error: any): boolean => {
  const errorMessage = typeof error === 'string' ? error : (error?.message || '');
  return errorMessage.toLowerCase().includes('email not confirmed') || 
         errorMessage.toLowerCase().includes('email_not_confirmed');
};

/**
 * 检查是否为请求频率限制错误
 * @param error 错误对象或消息
 * @returns 是否为请求频率限制错误
 */
export const isRateLimitError = (error: any): boolean => {
  const errorMessage = typeof error === 'string' ? error : (error?.message || '');
  return errorMessage.toLowerCase().includes('too many requests') || 
         errorMessage.toLowerCase().includes('rate_limit_exceeded') ||
         errorMessage.toLowerCase().includes('too_many_otp_attempts');
};

/**
 * 检查是否为OTP相关错误
 * @param error 错误对象或消息
 * @returns OTP错误类型或null
 */
export const getOtpErrorType = (error: any): 'expired' | 'invalid' | 'rate_limit' | null => {
  const errorMessage = typeof error === 'string' ? error : (error?.message || '');
  const lowerMessage = errorMessage.toLowerCase();
  
  if (lowerMessage.includes('expired') || lowerMessage.includes('token_expired') || lowerMessage.includes('otp_expired')) {
    return 'expired';
  }
  
  if (lowerMessage.includes('invalid') || lowerMessage.includes('invalid_token') || lowerMessage.includes('otp_invalid')) {
    return 'invalid';
  }
  
  if (lowerMessage.includes('too_many') || lowerMessage.includes('rate_limit')) {
    return 'rate_limit';
  }
  
  return null;
};
