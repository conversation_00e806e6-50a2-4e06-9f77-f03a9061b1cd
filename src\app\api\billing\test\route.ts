/**
 * 计费API测试端点
 * 用于测试专用计费API的功能
 * ⚠️ 仅用于开发测试，生产环境应删除此文件
 */
import { NextRequest, NextResponse } from 'next/server';

// 硬编码内部API密钥 - 与计费API保持一致
const INTERNAL_API_KEY = "billing_internal_2024_secure_key_xyz789";

/**
 * GET 方法 - 测试计费API连通性
 */
export async function GET() {
  return NextResponse.json({
    message: '计费API测试端点',
    timestamp: new Date().toISOString(),
    status: 'active'
  });
}

/**
 * POST 方法 - 测试计费API功能
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { testUserUuid, testAmount } = body;

    // 如果没有提供参数，使用默认测试参数
    const userUuid = testUserUuid || 'fb65f3a8-652d-46d1-b6ae-4ef148c1c729';
    const amount = testAmount || 100;

    console.log(`[Billing Test] 测试计费API - 用户: ${userUuid}, 金额: ${amount}`);

    // 构建完整的URL
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
    const billingUrl = `${baseUrl}/api/billing/deduct`;

    console.log(`[Billing Test] 调用URL: ${billingUrl}`);

    // 调用内部计费API
    const response = await fetch(billingUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        internalApiKey: INTERNAL_API_KEY,
        userUuid: userUuid,
        amount: amount,
        userAccessToken: undefined // 测试时不提供token
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      return NextResponse.json({
        success: false,
        message: `计费API调用失败: ${response.status}`,
        error: errorText
      });
    }

    const result = await response.json();

    return NextResponse.json({
      success: true,
      message: '计费API测试完成',
      billingResult: result,
      testInfo: {
        userUuid: userUuid,
        amount: amount,
        timestamp: new Date().toISOString(),
        billingUrl: billingUrl
      }
    });

  } catch (error) {
    console.error('[Billing Test] 测试异常:', error);
    return NextResponse.json(
      {
        success: false,
        error: '测试异常',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
