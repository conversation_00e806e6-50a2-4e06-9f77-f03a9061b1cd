'use client';

import React, { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';

// 会员信息类型定义
interface MembershipInfo {
  is_active: boolean;
  level: string;
  days_remaining: number | null;
  word_limit: number | null;
  word_used: number;
  word_remaining: number | null;
}

interface UserAccountModalProps {
  isOpen: boolean;
  onClose: () => void;
  userId: string;
  uid: string;
  membershipInfo: MembershipInfo | null;
  isLoading: boolean;
  onRecharge: () => void;
  onSignOut: () => void;
}

/**
 * 用户账号信息模态窗口组件
 */
const UserAccountModal: React.FC<UserAccountModalProps> = ({
  isOpen,
  onClose,
  userId,
  uid,
  membershipInfo,
  isLoading,
  onRecharge,
  onSignOut
}) => {
  // 添加客户端渲染检查
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);

    // 当模态窗口打开时，禁止body滚动
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    }

    // 清理函数：当组件卸载或模态窗口关闭时，恢复body滚动
    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  // 格式化数字显示
  const formatNumber = (num: number | null): string => {
    if (num === null || num === undefined) return '0';
    return num.toLocaleString();
  };

  // 格式化字数显示（使用中文万、亿单位）
  const formatWordCount = (count: number | null): string => {
    if (count === null || count === undefined) return '0字';

    if (count < 10000) {
      return `${count.toLocaleString()}字`;
    }

    if (count < 100000000) { // 小于1亿
      const wan = count / 10000;
      if (wan >= 10) {
        return `${Math.floor(wan)}万字`; // 整数万
      } else {
        return `${wan.toFixed(1)}万字`; // 保留1位小数
      }
    }

    // 大于等于1亿
    const yi = count / 100000000;
    if (yi >= 10) {
      return `${Math.floor(yi)}亿字`; // 整数亿
    } else {
      return `${yi.toFixed(1)}亿字`; // 保留1位小数
    }
  };

  // 格式化会员等级显示
  const formatMemberLevel = (level: string): string => {
    const levelMap: { [key: string]: string } = {
      '免费': '免费用户',
      '普通': '普通会员',
      '高级': '高级会员',
      '黑金': '黑金会员'
    };
    return levelMap[level] || level;
  };

  // 如果模态窗口未打开或组件未挂载，不渲染任何内容
  if (!isOpen || !isMounted) return null;

  // 使用createPortal将模态窗口渲染到body
  return createPortal(
    // 外部容器 (背景遮罩)
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999] p-4"
      onClick={onClose}
    >
      {/* 内部容器 (居中面板) */}
      <div
        className="bg-white shadow-xl rounded-xl w-full max-w-md"
        onClick={(e) => e.stopPropagation()} // 防止点击面板内部关闭
      >
        {/* 面板头部 */}
        <div className="flex justify-between items-center py-4 px-6 border-b border-slate-200">
          <h3 className="text-lg font-semibold text-slate-700">账号信息</h3>
          <button
            onClick={onClose}
            className="text-slate-400 hover:text-slate-600 p-1 rounded-full hover:bg-slate-100 transition-colors"
            aria-label="关闭"
          >
            <span className="material-icons text-xl">close</span>
          </button>
        </div>

        {/* 内容区域 */}
        <div className="p-6">
          {/* 用户基本信息 */}
          <div className="mb-6">
            <div className="mb-3">
              <p className="text-xs text-slate-500 mb-1">用户ID（显示名称）</p>
              <p className="text-sm font-medium text-slate-700">{userId}</p>
            </div>
            <div className="mb-3">
              <p className="text-xs text-slate-500 mb-1">UID</p>
              <p className="text-sm font-medium text-slate-700 break-all">{uid}</p>
            </div>
          </div>

          {/* 会员信息 */}
          {membershipInfo && (
            <div className="mb-6 p-4 bg-slate-50 rounded-lg border border-slate-200">
              <p className="text-xs text-slate-500 mb-2">会员信息</p>
              <p className="text-sm text-slate-700 mb-2">
                <span className="font-medium">{formatMemberLevel(membershipInfo.level)}</span>
                {membershipInfo.level !== '免费' && membershipInfo.days_remaining !== null && (
                  <span className="text-xs ml-2 text-slate-500">
                    (剩余: {membershipInfo.days_remaining}天)
                  </span>
                )}
              </p>
              
              {membershipInfo.level !== '免费' ? (
                <div className="space-y-1 text-xs">
                  <div className="flex justify-between">
                    <span className="text-slate-500">剩余字数:</span>
                    <span className="text-slate-700 font-medium">{formatWordCount(membershipInfo.word_remaining)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-500">字数限制:</span>
                    <span className="text-slate-700 font-medium">{formatWordCount(membershipInfo.word_limit)}</span>
                  </div>
                </div>
              ) : (
                <div className="space-y-1 text-xs">
                  <div className="flex justify-between">
                    <span className="text-slate-500">字数额度:</span>
                    <span className="text-slate-700 font-medium">0字 (需升级)</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-500">AI功能:</span>
                    <span className="text-green-600 font-medium">仅限免费模型</span>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* 加载状态 */}
          {isLoading && !membershipInfo && (
            <div className="mb-6 py-4 text-center text-slate-500 text-sm">
              <span>加载中...</span>
            </div>
          )}

          {/* 操作按钮 */}
          <div className="space-y-3">
            <button
              className="w-full bg-blue-500 text-white py-2.5 px-4 rounded-lg hover:bg-blue-600 transition-colors duration-200 text-sm font-medium"
              onClick={() => {
                onRecharge();
                onClose(); // 关闭当前模态窗口
              }}
            >
              充值
            </button>

            <button
              className="w-full bg-green-600 text-white py-2.5 px-4 rounded-lg hover:bg-green-700 transition-colors duration-200 text-sm font-medium"
              onClick={() => {
                onSignOut();
                onClose();
              }}
            >
              退出登录
            </button>
          </div>
        </div>
      </div>
    </div>,
    document.body
  );
};

export default UserAccountModal;
