/**
 * 用户API路由
 * 处理用户信息获取和登出
 */
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Supabase配置
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

// 创建Supabase客户端
const supabase = createClient(supabaseUrl, supabaseAnonKey);

/**
 * GET 方法 - 获取当前登录用户
 */
export async function GET(request: NextRequest) {
  try {
    // 从请求头获取Authorization token
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: '未提供认证令牌' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7); // 移除 "Bearer " 前缀

    // 使用token获取用户信息
    const { data: { user }, error } = await supabase.auth.getUser(token);

    if (error) {
      console.error('获取用户失败:', error);
      return NextResponse.json(
        { error: '获取用户信息失败' },
        { status: 401 }
      );
    }

    if (!user) {
      return NextResponse.json(
        { error: '用户未登录' },
        { status: 401 }
      );
    }

    return NextResponse.json({
      success: true,
      user: user
    });

  } catch (error) {
    console.error('获取当前用户失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

/**
 * DELETE 方法 - 用户登出
 */
export async function DELETE(request: NextRequest) {
  try {
    // 从请求头获取Authorization token
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: '未提供认证令牌' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7); // 移除 "Bearer " 前缀

    // 创建带token的客户端实例
    const supabaseWithAuth = createClient(supabaseUrl, supabaseAnonKey, {
      global: {
        headers: {
          Authorization: `Bearer ${token}`
        }
      }
    });

    const { error } = await supabaseWithAuth.auth.signOut();

    if (error) {
      console.error('登出失败:', error);
      return NextResponse.json(
        { error: '登出失败' },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      message: '登出成功'
    });

  } catch (error) {
    console.error('登出失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}
