/**
 * 同步下载API
 * 从云端下载TXT文件并解析为章节数据，用于智能同步功能
 */
import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { downloadFile } from '@/lib/minio';

// Supabase配置
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

/**
 * 服务器端获取当前用户
 */
async function getCurrentUser(request: NextRequest) {
  try {
    const supabaseServer = createServerClient(
      supabaseUrl,
      supabaseAnonKey,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll().map(cookie => ({
              name: cookie.name,
              value: cookie.value
            }));
          },
          setAll() {
            // 在API路由中不需要设置cookies
          },
        },
      }
    );

    const { data: { user }, error } = await supabaseServer.auth.getUser();

    if (error) {
      console.error('获取用户失败:', error);
      return null;
    }

    return user;
  } catch (error) {
    console.error('获取用户异常:', error);
    return null;
  }
}

/**
 * 解析TXT文件内容为章节数据
 * 复用前端的章节解析逻辑
 */
function parseChaptersFromText(text: string): Array<{order: number, title: string, content: string}> {
  // 过滤掉常见的电子书水印和版权声明
  const cleanText = text
    .replace(/☆[^☆]*?看帮网[^☆]*?☆/g, '')
    .replace(/☆本文由.*?所有☆/g, '')
    .replace(/☆请勿用于商业.*?自负☆/g, '')
    .replace(/☆https?:\/\/www\.kanbang\.cc☆/g, '');

  // 章节识别的正则表达式
  const chapterRegex = /(第[零一二两三四五六七八九十百千0-9]+章(\s+[^\n]+)?|第[0-9]{1,4}章(\s+[^\n]+)?|Chapter\s+[0-9]+(\s+[^\n]+)?|CHAPTER\s+[0-9]+(\s+[^\n]+)?)/gi;

  // 查找所有章节标记及其位置
  let chapterMatches: { title: string, index: number }[] = [];
  let match;

  while ((match = chapterRegex.exec(cleanText)) !== null) {
    chapterMatches.push({
      title: match[0].trim(),
      index: match.index
    });
  }

  // 如果没有找到章节，返回整个文本作为单章节
  if (chapterMatches.length === 0) {
    return [{
      order: 1,
      title: '第一章',
      content: cleanText.trim()
    }];
  }

  // 提取章节内容
  const chapters: Array<{order: number, title: string, content: string}> = [];

  for (let i = 0; i < chapterMatches.length; i++) {
    const current = chapterMatches[i];
    const next = chapterMatches[i + 1];

    // 计算章节标题的结束位置（标题之后的内容）
    const titleEndIndex = current.index + current.title.length;

    const chapterContent = next
      ? cleanText.substring(titleEndIndex, next.index).trim()
      : cleanText.substring(titleEndIndex).trim();

    chapters.push({
      order: i + 1,
      title: current.title,
      content: chapterContent
    });
  }

  return chapters;
}

/**
 * GET 方法 - 下载并解析云端文件
 */
export async function GET(request: NextRequest) {
  try {
    // 获取当前用户
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json(
        { error: '用户未登录' },
        { status: 401 }
      );
    }

    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const workTitle = searchParams.get('workTitle');
    const fileId = searchParams.get('fileId');

    if (!workTitle && !fileId) {
      return NextResponse.json(
        { error: '缺少必需参数：workTitle 或 fileId' },
        { status: 400 }
      );
    }

    // 创建Supabase客户端
    const supabaseServer = createServerClient(
      supabaseUrl,
      supabaseAnonKey,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll().map(cookie => ({
              name: cookie.name,
              value: cookie.value
            }));
          },
          setAll() {},
        },
      }
    );

    // 查询文件记录
    let query = supabaseServer
      .from('novel_files')
      .select('*')
      .eq('user_id', user.id);

    if (fileId) {
      query = query.eq('id', fileId);
    } else if (workTitle) {
      query = query.eq('work_title', workTitle);
    }

    const { data: files, error } = await query;

    if (error || !files || files.length === 0) {
      return NextResponse.json(
        { error: '文件不存在或无权限访问' },
        { status: 404 }
      );
    }

    // 如果有多个文件，选择最新的
    const fileRecord = files.sort((a, b) => 
      new Date(b.upload_time).getTime() - new Date(a.upload_time).getTime()
    )[0];

    console.log(`开始下载并解析文件: ${fileRecord.minio_object_key}`);

    // 从MinIO下载文件
    const fileStream = await downloadFile(fileRecord.minio_object_key);

    // 将流转换为Buffer
    const chunks: Buffer[] = [];
    for await (const chunk of fileStream) {
      chunks.push(chunk);
    }
    const fileBuffer = Buffer.concat(chunks);

    // 转换为文本
    const text = fileBuffer.toString('utf-8');

    // 解析章节
    const chapters = parseChaptersFromText(text);

    console.log(`文件解析完成: ${fileRecord.minio_object_key}, 章节数: ${chapters.length}`);

    return NextResponse.json({
      success: true,
      data: {
        fileInfo: {
          id: fileRecord.id,
          work_title: fileRecord.work_title,
          file_name: fileRecord.file_name,
          upload_time: fileRecord.upload_time,
          file_size: fileRecord.file_size
        },
        chapters: chapters,
        totalChapters: chapters.length,
        totalWords: chapters.reduce((sum, chapter) => sum + chapter.content.length, 0)
      }
    });

  } catch (error) {
    console.error('下载并解析文件失败:', error);
    return NextResponse.json(
      { 
        error: '下载并解析文件失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
