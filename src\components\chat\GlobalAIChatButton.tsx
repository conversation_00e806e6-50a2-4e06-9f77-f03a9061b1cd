'use client';

import React, { useState, useEffect } from 'react';
import { useAIChat } from '@/contexts/AIChatContext';

export const GlobalAIChatButton: React.FC = () => {
  const { isOpen, toggleChat } = useAIChat();
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // 处理快捷键 Ctrl+K 或 Cmd+K
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        toggleChat();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [toggleChat]);

  if (!isMounted || isOpen) return null;

  return (
    <button
      onClick={toggleChat}
      className="fixed top-1/2 right-0 transform -translate-y-1/2 px-3 py-6 bg-gradient-to-b from-[#5a9d6b] to-[#65ad79] text-white rounded-l-lg shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300 z-[9997] flex flex-col items-center justify-center group"
      title="打开对话操作助手 (Ctrl+K)"
      style={{ writingMode: 'vertical-rl', textOrientation: 'mixed' }}
    >
      <span className="text-sm font-medium tracking-wider">对话操作助手</span>

      {/* 快捷键提示 */}
      <div className="absolute right-full top-1/2 transform -translate-y-1/2 mr-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
        Ctrl+K 快速打开
      </div>
    </button>
  );
};
