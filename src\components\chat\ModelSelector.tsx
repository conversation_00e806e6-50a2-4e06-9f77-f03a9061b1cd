'use client';

import React, { useState } from 'react';
import { MODELS } from '@/lib/AIserver';

interface ModelSelectorProps {
  selectedModel: string;
  onModelChange: (model: string) => void;
  disabled?: boolean;
}

// 模型配置
const MODEL_CONFIG = {
  [MODELS.LLM_TEST]: {
    name: '测试版',
    description: '测试版AI模型，免费使用',
    icon: 'science',
    color: 'text-blue-600'
  },
  [MODELS.LLM_CLAUDE]: {
    name: '克劳德',
    description: 'Claude Sonnet 4，顶级推理能力',
    icon: 'psychology',
    color: 'text-purple-600'
  }
};

export const ModelSelector: React.FC<ModelSelectorProps> = ({
  selectedModel,
  onModelChange,
  disabled = false
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const currentModel = MODEL_CONFIG[selectedModel];

  const handleModelSelect = (model: string) => {
    onModelChange(model);
    setIsOpen(false);
  };

  return (
    <div className="relative">
      {/* 选择器按钮 */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        disabled={disabled}
        className="flex items-center space-x-2 px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <span className={`material-icons text-sm ${currentModel.color}`}>
          {currentModel.icon}
        </span>
        <span className="text-sm font-medium text-gray-700">
          {currentModel.name}
        </span>
        <span className={`material-icons text-sm text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`}>
          expand_more
        </span>
      </button>

      {/* 下拉菜单 */}
      {isOpen && (
        <>
          {/* 遮罩层 */}
          <div
            className="fixed inset-0 z-10"
            onClick={() => setIsOpen(false)}
          />
          
          {/* 菜单内容 */}
          <div className="absolute top-full left-0 mt-1 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-20">
            <div className="p-2">
              {Object.entries(MODEL_CONFIG).map(([modelKey, config]) => (
                <button
                  key={modelKey}
                  onClick={() => handleModelSelect(modelKey)}
                  className={`w-full flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors ${
                    selectedModel === modelKey ? 'bg-blue-50 border border-blue-200' : ''
                  }`}
                >
                  <span className={`material-icons text-lg ${config.color} mt-0.5`}>
                    {config.icon}
                  </span>
                  <div className="flex-1 text-left">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-gray-900">
                        {config.name}
                      </span>
                      {selectedModel === modelKey && (
                        <span className="material-icons text-sm text-blue-600">
                          check_circle
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 mt-1">
                      {config.description}
                    </p>
                  </div>
                </button>
              ))}
            </div>
            
            {/* 提示信息 */}
            <div className="border-t border-gray-200 p-3 bg-gray-50 rounded-b-lg">
              <p className="text-xs text-gray-600">
                <span className="material-icons text-xs mr-1">info</span>
                不同模型有不同的计费标准，高级版模型消耗更多字符
              </p>
            </div>
          </div>
        </>
      )}
    </div>
  );
};
