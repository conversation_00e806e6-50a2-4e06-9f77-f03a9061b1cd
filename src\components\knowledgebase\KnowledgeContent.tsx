import React, { useState } from 'react';
import { Knowledge } from '@/data';
import DeleteConfirmDialog from './DeleteConfirmDialog';

interface KnowledgeContentProps {
  knowledge: Knowledge;
  isSaving: boolean;
  onTitleChange: (title: string) => void;
  onContentChange: (content: string) => void;
  onSave?: () => void;
  onDelete?: () => void;
  onAddTag: (tag: string) => void;
  onRemoveTag: (index: number) => void;
}

const KnowledgeContent: React.FC<KnowledgeContentProps> = ({
  knowledge,
  isSaving,
  onTitleChange,
  onContentChange,
  onSave,
  onDelete,
  onAddTag,
  onRemoveTag
}) => {
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [newTagInput, setNewTagInput] = useState('');

  const handleAddTag = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && newTagInput.trim()) {
      onAddTag(newTagInput.trim());
      setNewTagInput('');
    }
  };

  return (
    <>
      {/* 知识标题和操作区 */}
      <div className="p-5 border-b border-[rgba(125,133,204,0.2)] bg-[rgba(125,133,204,0.03)] flex justify-between items-center">
        <div className="flex items-center flex-1">
          <input
            type="text"
            value={knowledge.title}
            onChange={(e) => onTitleChange(e.target.value)}
            placeholder="输入知识标题..."
            className="w-full text-xl font-medium text-text-dark bg-transparent border-none focus:outline-none focus:ring-0 font-ma-shan"
          />
        </div>
        <div className="flex items-center">
          {isSaving ? (
            <span className="text-[#7D85CC] flex items-center text-sm">
              <span className="material-icons text-sm mr-1 animate-spin">sync</span>
              保存中...
            </span>
          ) : (
            <span className="flex items-center text-sm text-[#7D85CC]">
              <span className="material-icons text-sm mr-1">check_circle</span>
              自动保存
            </span>
          )}
        </div>
      </div>



      {/* 知识内容编辑区 */}
      <div className="flex-1 p-6 overflow-y-auto relative">
        <textarea
          value={knowledge.content}
          onChange={(e) => onContentChange(e.target.value)}
          placeholder="输入知识内容..."
          className="w-full h-full p-4 bg-white border border-[rgba(125,133,204,0.2)] rounded-lg text-lg focus:outline-none focus:ring-1 focus:ring-[#7D85CC] resize-none shadow-inner"
          style={{
            fontFamily: "'Source Han Sans', 'Noto Sans SC', sans-serif",
            lineHeight: '1.8'
          }}
        ></textarea>
      </div>

      {/* 底部状态栏 */}
      <div className="px-4 py-2 border-t border-[rgba(125,133,204,0.1)] flex justify-between items-center text-xs text-text-light bg-[rgba(125,133,204,0.03)]">
        <div className="flex items-center">
          <span className="material-icons text-[#7D85CC] text-sm mr-1">update</span>
          最后更新: {new Date(knowledge.updatedAt).toLocaleString()}
        </div>
        <div className="flex items-center">
          {isSaving ? (
            <span className="text-[#7D85CC] flex items-center">
              <span className="material-icons text-xs mr-1 animate-spin">sync</span>
              保存中...
            </span>
          ) : (
            <span className="flex items-center">
              <span className="material-icons text-[#7D85CC] text-xs mr-1">check_circle</span>
              自动保存
            </span>
          )}
        </div>
      </div>

      {/* 删除确认对话框 */}
      {showDeleteConfirm && onDelete && (
        <DeleteConfirmDialog
          title={knowledge.title}
          onCancel={() => setShowDeleteConfirm(false)}
          onConfirm={() => {
            setShowDeleteConfirm(false);
            onDelete();
          }}
        />
      )}
    </>
  );
};

export default KnowledgeContent;
