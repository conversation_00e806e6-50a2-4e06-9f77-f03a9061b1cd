/**
 * 密码重置OTP API路由
 * 处理密码重置验证码发送和验证
 */
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { handleSupabaseAuthError } from '@/lib/utils/ErrorHandler';

// Supabase配置
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

// 创建Supabase客户端
const supabase = createClient(supabaseUrl, supabaseAnonKey);

/**
 * POST 方法 - 发送密码重置OTP
 */
export async function POST(request: NextRequest) {
  try {
    // 解析请求体
    const body = await request.json();
    const { email } = body;

    // 验证请求数据
    if (!email) {
      return NextResponse.json(
        { success: false, message: '邮箱是必填项' },
        { status: 400 }
      );
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { success: false, message: '邮箱格式不正确' },
        { status: 400 }
      );
    }

    // 验证邮箱域名
    const allowedDomains = ['qq.com', '163.com', 'gmail.com'];
    const emailDomain = email.split('@')[1]?.toLowerCase();
    if (!emailDomain || !allowedDomains.includes(emailDomain)) {
      return NextResponse.json(
        { success: false, message: '只支持 @qq.com、@163.com 和 @gmail.com 邮箱' },
        { status: 400 }
      );
    }

    console.log('发送密码重置OTP:', email);

    // 使用Supabase发送密码重置OTP（不使用redirectTo，启用OTP模式）
    const { error } = await supabase.auth.resetPasswordForEmail(email);

    if (error) {
      console.error('发送密码重置OTP失败:', error);

      // 使用统一错误处理
      const errorMessage = handleSupabaseAuthError(error);

      return NextResponse.json(
        { success: false, message: errorMessage },
        { status: 400 }
      );
    }

    console.log('密码重置OTP发送成功:', email);

    return NextResponse.json({
      success: true,
      message: '验证码已发送到您的邮箱，请查收'
    });

  } catch (error) {
    console.error('发送密码重置OTP异常:', error);
    return NextResponse.json(
      { success: false, message: '服务器内部错误' },
      { status: 500 }
    );
  }
}

/**
 * PUT 方法 - 验证OTP并重置密码
 */
export async function PUT(request: NextRequest) {
  try {
    // 解析请求体
    const body = await request.json();
    const { email, token, password } = body;

    // 验证请求数据
    if (!email || !token || !password) {
      return NextResponse.json(
        { success: false, message: '邮箱、验证码和新密码都是必填项' },
        { status: 400 }
      );
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { success: false, message: '邮箱格式不正确' },
        { status: 400 }
      );
    }

    // 验证OTP格式（6位数字）
    const otpRegex = /^\d{6}$/;
    if (!otpRegex.test(token)) {
      return NextResponse.json(
        { success: false, message: '验证码必须是6位数字' },
        { status: 400 }
      );
    }

    // 验证密码长度
    if (password.length < 6) {
      return NextResponse.json(
        { success: false, message: '密码长度至少为6个字符' },
        { status: 400 }
      );
    }

    console.log('验证密码重置OTP:', email, 'token:', token);

    // 使用Supabase验证OTP并重置密码
    const { data, error } = await supabase.auth.verifyOtp({
      email,
      token,
      type: 'recovery'  // 密码重置类型
    });

    if (error) {
      console.error('密码重置OTP验证失败:', error);

      // 使用统一错误处理
      const errorMessage = handleSupabaseAuthError(error);

      return NextResponse.json(
        { success: false, message: errorMessage },
        { status: 400 }
      );
    }

    if (!data.user || !data.session) {
      return NextResponse.json(
        { success: false, message: '验证失败，请重试' },
        { status: 400 }
      );
    }

    console.log('密码重置OTP验证成功，开始更新密码:', data.user.email);

    // 使用验证后的session直接更新密码
    const { error: updateError } = await supabase.auth.updateUser(
      { password: password },
      { accessToken: data.session.access_token }
    );

    if (updateError) {
      console.error('更新密码失败:', updateError);
      return NextResponse.json(
        { success: false, message: '密码更新失败，请重试' },
        { status: 400 }
      );
    }

    console.log('密码重置成功:', data.user.email);

    // 返回成功响应，包含会话信息
    return NextResponse.json({
      success: true,
      message: '密码重置成功',
      user: data.user,
      session: data.session
    });

  } catch (error) {
    console.error('密码重置异常:', error);
    return NextResponse.json(
      { success: false, message: '服务器内部错误' },
      { status: 500 }
    );
  }
}
