/**
 * 知识库前端类型定义
 * 复用现有IndexedDB结构
 */

// 导入数据库中的知识库类型
import { Knowledge as DBKnowledge } from '@/data/database/types/knowledge';

// 前端知识库类型（复用数据库类型）
export type Knowledge = DBKnowledge;

// 知识库表单数据
export interface KnowledgeFormData {
  title: string;
  content: string;
  tags?: string[];
}

// 知识库模态窗口属性
export interface KnowledgeModalProps {
  isOpen: boolean;
  onClose: () => void;
  workId: number;
}

// 知识库编辑器属性
export interface KnowledgeEditorProps {
  knowledge: Knowledge;
  workId: number;
  onSave: (knowledge: Knowledge) => void;
  isSaving: boolean;
  lastSavedAt: Date | null;
}

// 知识库列表项属性
export interface KnowledgeListItemProps {
  knowledge: Knowledge;
  isActive: boolean;
  onClick: (knowledgeId: number) => void;
  onDelete?: (knowledgeId: number) => void;
}
