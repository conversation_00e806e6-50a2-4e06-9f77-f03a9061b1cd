'use client';

import React, { useState } from 'react';

interface ConflictItem {
  workTitle: string;
  localTime: Date;
  cloudTime: Date;
  status: 'local_newer' | 'cloud_newer' | 'local_only' | 'cloud_only';
}

interface ConflictResolutionDialogProps {
  isOpen: boolean;
  conflicts: ConflictItem[];
  onResolve: (resolutions: { [workTitle: string]: 'use_local' | 'use_cloud' | 'skip' }) => void;
  onCancel: () => void;
}

export const ConflictResolutionDialog: React.FC<ConflictResolutionDialogProps> = ({
  isOpen,
  conflicts,
  onResolve,
  onCancel
}) => {
  const [resolutions, setResolutions] = useState<{ [workTitle: string]: 'use_local' | 'use_cloud' | 'skip' }>({});

  if (!isOpen) return null;

  const handleResolutionChange = (workTitle: string, resolution: 'use_local' | 'use_cloud' | 'skip') => {
    setResolutions(prev => ({
      ...prev,
      [workTitle]: resolution
    }));
  };

  const handleBatchUseLocal = () => {
    const newResolutions: { [workTitle: string]: 'use_local' | 'use_cloud' | 'skip' } = {};
    conflicts.forEach(conflict => {
      newResolutions[conflict.workTitle] = 'use_local';
    });
    setResolutions(newResolutions);
  };

  const handleBatchUseCloud = () => {
    const newResolutions: { [workTitle: string]: 'use_local' | 'use_cloud' | 'skip' } = {};
    conflicts.forEach(conflict => {
      newResolutions[conflict.workTitle] = 'use_cloud';
    });
    setResolutions(newResolutions);
  };

  const handleConfirm = () => {
    // 为未设置的冲突项设置默认值
    const finalResolutions = { ...resolutions };
    conflicts.forEach(conflict => {
      if (!finalResolutions[conflict.workTitle]) {
        finalResolutions[conflict.workTitle] = 'skip';
      }
    });

    onResolve(finalResolutions);
  };

  const formatTime = (date: Date) => {
    return new Intl.DateTimeFormat('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    }).format(date);
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'local_newer':
        return '本地更新';
      case 'cloud_newer':
        return '云端更新';
      case 'local_only':
        return '仅本地';
      case 'cloud_only':
        return '仅云端';
      default:
        return status;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'local_newer':
        return 'text-blue-600';
      case 'cloud_newer':
        return 'text-green-600';
      case 'local_only':
        return 'text-orange-600';
      case 'cloud_only':
        return 'text-purple-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[80vh] overflow-hidden">
        {/* 标题栏 */}
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-800">
            解决同步冲突
          </h2>
          <p className="text-sm text-gray-600 mt-1">
            检测到 {conflicts.length} 个作品存在同步冲突，请选择处理方式
          </p>
        </div>



        {/* 冲突列表 */}
        <div className="overflow-y-auto max-h-96">
          {conflicts.map((conflict) => (
            <div key={conflict.workTitle} className="px-6 py-4 border-b border-gray-100">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h3 className="font-medium text-gray-800">{conflict.workTitle}</h3>
                  <div className="mt-2 space-y-1 text-sm text-gray-600">
                    <div className="flex items-center gap-4">
                      <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(conflict.status)}`}>
                        {getStatusText(conflict.status)}
                      </span>
                    </div>
                    <div>本地时间：{formatTime(conflict.localTime)}</div>
                    <div>云端时间：{formatTime(conflict.cloudTime)}</div>
                  </div>
                </div>
                
                <div className="ml-4 flex flex-col gap-2">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name={`resolution-${conflict.workTitle}`}
                      value="use_local"
                      checked={resolutions[conflict.workTitle] === 'use_local'}
                      onChange={() => handleResolutionChange(conflict.workTitle, 'use_local')}
                      className="mr-2"
                    />
                    <span className="text-sm">使用本地版本</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name={`resolution-${conflict.workTitle}`}
                      value="use_cloud"
                      checked={resolutions[conflict.workTitle] === 'use_cloud'}
                      onChange={() => handleResolutionChange(conflict.workTitle, 'use_cloud')}
                      className="mr-2"
                    />
                    <span className="text-sm">使用云端版本</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name={`resolution-${conflict.workTitle}`}
                      value="skip"
                      checked={resolutions[conflict.workTitle] === 'skip'}
                      onChange={() => handleResolutionChange(conflict.workTitle, 'skip')}
                      className="mr-2"
                    />
                    <span className="text-sm">跳过</span>
                  </label>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* 按钮栏 */}
        <div className="px-6 py-4 border-t border-gray-200 flex justify-between">
          <div className="flex gap-3">
            <button
              onClick={handleBatchUseLocal}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              全部使用本地
            </button>
            <button
              onClick={handleBatchUseCloud}
              className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600"
            >
              全部使用云端
            </button>
          </div>
          <div className="flex gap-3">
            <button
              onClick={onCancel}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50"
            >
              取消
            </button>
            <button
              onClick={handleConfirm}
              className="px-4 py-2 bg-[#00C250] text-white rounded hover:bg-[#00A844]"
            >
              确认处理
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConflictResolutionDialog;
