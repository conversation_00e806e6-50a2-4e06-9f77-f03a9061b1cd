/**
 * 通用提示词选择模态窗口组件 - 用户提示词选择
 */
import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Modal } from '@/components/common/modals';
import PromptFormModal from './PromptFormModal';

// 用户提示词接口
interface UserPrompt {
  id: string;
  title: string;
  description?: string;
  type: string;
  content?: string;
  created_at: string;
  updated_at: string;
  created_by?: string;
  usage_count?: number;
}

interface PromptSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (prompt: UserPrompt) => void;
  promptType?: 'ai_writing' | 'ai_polishing';
  initialSelectedId?: string;
}

// 提示词类型标签映射
const PROMPT_TYPE_LABELS_MAP = {
  'ai_writing': 'AI写作',
  'ai_polishing': 'AI润色'
};



/**
 * 通用提示词选择模态窗口组件
 */
export const PromptSelectionModal: React.FC<PromptSelectionModalProps> = ({
  isOpen,
  onClose,
  onSelect,
  promptType,
  initialSelectedId
}) => {
  // 状态
  const [userPrompts, setUserPrompts] = useState<UserPrompt[]>([]);
  const [filteredPrompts, setFilteredPrompts] = useState<UserPrompt[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPrompt, setSelectedPrompt] = useState<UserPrompt | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>(promptType || 'ai_writing');
  const [showCreateModal, setShowCreateModal] = useState(false);

  // 当promptType变化时更新selectedCategory
  useEffect(() => {
    if (promptType) {
      setSelectedCategory(promptType);
    }
  }, [promptType]);

  // 滚动容器引用
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // 加载用户提示词
  const loadUserPrompts = useCallback(async () => {
    try {
      const response = await fetch('/api/prompt', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          category: 'userprompt',
          type: selectedCategory,
          limit: 50
        })
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          const prompts = data.data || [];
          setUserPrompts(prompts);
          return prompts;
        }
      }
      return [];
    } catch (error) {
      console.error('加载用户提示词失败:', error);
      throw error;
    }
  }, [selectedCategory]);

  // 加载提示词
  const loadPrompts = useCallback(async () => {
    if (!isOpen) return;

    setIsLoading(true);
    setError('');

    try {
      const userPromptsData = await loadUserPrompts();

      // 如果有初始选中的提示词ID，设置选中状态
      if (initialSelectedId) {
        const selected = userPromptsData.find((p: UserPrompt) => String(p.id) === String(initialSelectedId));
        if (selected) {
          setSelectedPrompt(selected);
        }
      }
    } catch (error) {
      console.error('加载提示词失败:', error);
      setError('加载提示词失败');
    } finally {
      setIsLoading(false);
    }
  }, [isOpen, loadUserPrompts, initialSelectedId]);

  // 过滤提示词
  useEffect(() => {
    let filtered = userPrompts;

    // 搜索过滤
    if (searchTerm) {
      filtered = filtered.filter(prompt =>
        prompt.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (prompt.description && prompt.description.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    setFilteredPrompts(filtered);
  }, [userPrompts, searchTerm]);

  // 加载提示词
  useEffect(() => {
    loadPrompts();
  }, [loadPrompts]);

  // 处理提示词选择（只选中，不关闭窗口）
  const handlePromptSelect = (prompt: UserPrompt) => {
    setSelectedPrompt(prompt);
  };

  // 处理确认选择
  const handleConfirmSelect = () => {
    if (selectedPrompt) {
      onSelect(selectedPrompt);
      onClose();
    }
  };

  // 处理创建提示词
  const handleCreatePrompt = () => {
    setShowCreateModal(true);
  };

  // 处理保存提示词
  const handleSavePrompt = async (savedPrompt: UserPrompt) => {
    // 重新加载提示词列表
    await loadPrompts();
    // 只选中新创建的提示词，不自动确认选择
    setSelectedPrompt(savedPrompt);
    setShowCreateModal(false);
  };



  // 底部按钮组件
  const footerButtons = (
    <div className="flex justify-end space-x-4">
      <button
        onClick={onClose}
        className="px-6 py-3 text-gray-600 hover:text-gray-800 transition-all duration-300 font-medium hover:bg-gray-100/50 rounded-xl"
      >
        取消
      </button>
      <button
        onClick={handleConfirmSelect}
        disabled={!selectedPrompt}
        className={`relative px-8 py-3 rounded-xl font-medium transition-all duration-300 transform ${
          selectedPrompt
            ? 'bg-gradient-to-r from-[#00C250] to-[#00A844] text-white hover:from-[#00A844] hover:to-[#008A3A] hover:scale-105 hover:shadow-lg hover:shadow-[rgba(0,194,80,0.3)] active:scale-95'
            : 'bg-gray-200 text-gray-400 cursor-not-allowed opacity-60'
        }`}
      >
        {selectedPrompt && (
          <div className="absolute inset-0 bg-white/20 rounded-xl opacity-0 hover:opacity-100 transition-opacity duration-300"></div>
        )}
        <span className="relative z-10 flex items-center">
          <span className="material-icons text-sm mr-2">check_circle</span>
          确认选择
        </span>
      </button>
    </div>
  );

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="选择提示词"
      maxWidth="max-w-4xl"
      footer={footerButtons}
    >
      {/* 背景装饰 */}
      <div className="absolute inset-0 bg-gradient-to-br from-[rgba(120,180,140,0.03)] to-[rgba(90,157,107,0.05)] rounded-xl"></div>
      <div className="absolute inset-0 opacity-30" style={{
        backgroundImage: `url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23rgba(120,180,140,0.08)' fill-opacity='1'%3E%3Ccircle cx='3' cy='3' r='1'/%3E%3C/g%3E%3C/svg%3E")`
      }}></div>

      <div className="relative flex flex-col h-full">
        {/* 分类选择和搜索栏 */}
        <div className="mb-6 space-y-4">
          {/* 分类选择 */}
          <div className="flex space-x-3">
            {Object.entries(PROMPT_TYPE_LABELS_MAP).map(([type, label]) => (
              <button
                key={type}
                onClick={() => setSelectedCategory(type)}
                className={`group relative px-6 py-3 rounded-full text-sm font-medium transition-all duration-300 transform hover:scale-105 ${
                  selectedCategory === type
                    ? 'bg-gradient-to-r from-[#5a9d6b] to-[#4a8d5b] text-white shadow-lg shadow-[rgba(90,157,107,0.3)] ring-2 ring-[rgba(90,157,107,0.2)]'
                    : 'bg-white/70 backdrop-blur-sm text-gray-700 hover:bg-white/90 hover:shadow-md border border-[rgba(120,180,140,0.2)]'
                }`}
              >
                <span className="relative z-10 flex items-center">
                  <span className="material-icons text-sm mr-2">
                    {type === 'ai_writing' ? 'edit' : 'auto_fix_high'}
                  </span>
                  {label}
                </span>
                {selectedCategory === type && (
                  <div className="absolute inset-0 bg-gradient-to-r from-[#5a9d6b] to-[#4a8d5b] rounded-full animate-pulse opacity-20"></div>
                )}
              </button>
            ))}
          </div>

          {/* 搜索栏 */}
          <div className="flex gap-4">
            <div className="relative flex-1">
              <div className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10">
                <span className="material-icons text-[rgba(120,180,140,0.6)] text-lg transition-colors duration-300">
                  search
                </span>
              </div>
              <input
                type="text"
                placeholder="搜索提示词..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-12 pr-4 py-3 bg-white/80 backdrop-blur-sm border border-[rgba(120,180,140,0.2)] rounded-xl focus:outline-none focus:ring-2 focus:ring-[rgba(90,157,107,0.3)] focus:border-[rgba(90,157,107,0.4)] focus:bg-white/90 transition-all duration-300 text-gray-700 placeholder-gray-400 shadow-sm hover:shadow-md"
              />
            </div>
            <button
              onClick={handleCreatePrompt}
              className="group relative flex items-center px-6 py-3 bg-gradient-to-r from-[#00C250] to-[#00A844] text-white rounded-xl hover:from-[#00A844] hover:to-[#008A3A] transition-all duration-300 transform hover:scale-105 hover:shadow-lg hover:shadow-[rgba(0,194,80,0.3)] font-medium"
            >
              <span className="material-icons text-sm mr-2 group-hover:rotate-90 transition-transform duration-300">add</span>
              <span>创建提示词</span>
              <div className="absolute inset-0 bg-white/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </button>
          </div>
        </div>

        {/* 提示词列表 */}
        <div className="flex-1 flex flex-col min-h-0 bg-white/30 backdrop-blur-sm rounded-xl border border-[rgba(120,180,140,0.15)] shadow-inner">
          <div
            ref={scrollContainerRef}
            className="flex-1 overflow-y-auto p-4 custom-scrollbar"
            style={{
              scrollbarWidth: 'thin'
            }}
          >
            {isLoading ? (
              <div className="h-full flex items-center justify-center">
                <div className="text-center">
                  <div className="relative">
                    <div className="animate-spin rounded-full h-12 w-12 border-4 border-[rgba(90,157,107,0.2)] border-t-[#5a9d6b] mx-auto mb-4"></div>
                    <div className="absolute inset-0 rounded-full bg-gradient-to-r from-[#5a9d6b] to-[#4a8d5b] opacity-20 animate-pulse"></div>
                  </div>
                  <p className="text-gray-600 font-medium">加载中...</p>
                </div>
              </div>
            ) : error ? (
              <div className="h-full flex items-center justify-center">
                <div className="text-center bg-red-50/80 backdrop-blur-sm rounded-xl p-6 border border-red-200/50">
                  <div className="w-16 h-16 bg-gradient-to-br from-red-400 to-red-500 rounded-full flex items-center justify-center mb-4 mx-auto shadow-lg">
                    <span className="material-icons text-3xl text-white">error_outline</span>
                  </div>
                  <p className="text-red-700 font-medium">{error}</p>
                </div>
              </div>
            ) : filteredPrompts.length === 0 ? (
              <div className="h-full flex items-center justify-center flex-col">
                <div className="relative mb-6">
                  <div className="w-24 h-24 bg-gradient-to-br from-[#f7f2ea] to-[#ede8d3] rounded-full flex items-center justify-center shadow-lg border border-[rgba(120,180,140,0.2)]">
                    <span className="material-icons text-5xl text-[#8a7c70]">lightbulb_outline</span>
                  </div>
                  <div className="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-br from-yellow-300 to-yellow-400 rounded-full flex items-center justify-center shadow-md animate-bounce">
                    <span className="material-icons text-sm text-yellow-800">star</span>
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-gray-700 mb-3 font-ma-shan">
                  {searchTerm ? "没有找到匹配的提示词" : "暂无提示词"}
                </h3>
                <p className="text-gray-500 text-center max-w-xs mb-6 leading-relaxed">
                  {searchTerm ? "尝试使用其他关键词搜索，或创建一个新的提示词" : "点击上方的创建按钮来添加您的第一个提示词"}
                </p>
                {!searchTerm && (
                  <button
                    onClick={handleCreatePrompt}
                    className="px-6 py-2 bg-gradient-to-r from-[#5a9d6b] to-[#4a8d5b] text-white rounded-full hover:shadow-lg transition-all duration-300 transform hover:scale-105 font-medium"
                  >
                    立即创建
                  </button>
                )}
              </div>
            ) : (
              <div className="space-y-3">
                {filteredPrompts.map((prompt, index) => (
                  <div
                    key={prompt.id}
                    className={`group relative p-4 rounded-xl transition-all duration-300 cursor-pointer transform hover:scale-[1.02] animate-fadeIn ${
                      selectedPrompt?.id === prompt.id
                        ? 'bg-gradient-to-r from-[#e6dfd0] to-[#f0e9df] border-2 border-[#6d5c4d] shadow-lg shadow-[rgba(109,92,77,0.2)] ring-2 ring-[rgba(109,92,77,0.1)]'
                        : 'bg-white/80 backdrop-blur-sm border border-[rgba(120,180,140,0.15)] hover:bg-white/95 hover:border-[rgba(90,157,107,0.3)] hover:shadow-md'
                    }`}
                    style={{ animationDelay: `${index * 50}ms` }}
                    onClick={() => handlePromptSelect(prompt)}
                  >
                    {/* 选中状态的装饰 */}
                    {selectedPrompt?.id === prompt.id && (
                      <>
                        <div className="absolute -top-1 -right-1 w-6 h-6 bg-gradient-to-br from-[#6d5c4d] to-[#5a4a3d] rounded-full flex items-center justify-center shadow-lg">
                          <span className="material-icons text-white text-sm">check</span>
                        </div>
                        <div className="absolute inset-0 bg-gradient-to-r from-[rgba(109,92,77,0.05)] to-[rgba(109,92,77,0.1)] rounded-xl animate-pulse"></div>
                      </>
                    )}

                    {/* 胶带装饰 */}
                    <div className="absolute -top-2 left-6 w-16 h-6 bg-gradient-to-r from-yellow-300/70 to-yellow-400/70 rounded-sm transform -rotate-2 shadow-sm opacity-60"></div>
                    <div className="relative z-10">
                      {/* 顶部区域：标题行和右上角信息 */}
                      <div className="flex items-start justify-between mb-2">
                        {/* 左侧标题区域 */}
                        <div className="flex items-center flex-1 min-w-0 pr-4">
                          <div className="w-8 h-8 rounded-full bg-gradient-to-br from-[rgba(90,157,107,0.2)] to-[rgba(90,157,107,0.3)] flex items-center justify-center mr-3 shadow-sm">
                            <span className="material-icons text-sm text-[#5a9d6b]">lightbulb</span>
                          </div>
                          <h3 className="font-semibold text-gray-800 truncate mr-3 text-lg">
                            {prompt.title}
                          </h3>
                          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium shadow-sm bg-gradient-to-r from-orange-50 to-amber-50 text-orange-700 border border-orange-200">
                            <span className="material-icons text-xs mr-1">trending_up</span>
                            已使用 {prompt.usage_count || 0} 次
                          </span>
                        </div>

                        {/* 右上角信息区域 - 单行显示 */}
                        <div className="flex items-center text-xs text-gray-400 flex-shrink-0">
                          <span className="material-icons text-xs mr-1">schedule</span>
                          <span>更新于 {new Date(prompt.updated_at).toLocaleDateString()}</span>
                          <span className="mx-2">•</span>
                          <span className="material-icons text-xs mr-1">person</span>
                          <span>我的提示词</span>
                        </div>
                      </div>

                      {/* 底部描述区域 - 占满整行 */}
                      {prompt.description && (
                        <p className="text-sm text-gray-600 line-clamp-1 leading-relaxed">
                          {prompt.description}
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 创建提示词模态窗口 */}
      {showCreateModal && (
        <PromptFormModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          onSave={handleSavePrompt}
          prompt={null}
          promptType={selectedCategory}
        />
      )}
    </Modal>
  );
};

export default PromptSelectionModal;
