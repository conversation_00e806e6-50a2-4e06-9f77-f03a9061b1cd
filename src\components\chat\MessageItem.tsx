'use client';

import React, { useState, useEffect, useRef } from 'react';

// 聊天消息接口
interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  isStreaming?: boolean;
}

interface MessageItemProps {
  message: ChatMessage;
  onEditMessage?: (messageId: string, newContent: string) => void;
  onRegenerateMessage?: (messageId: string) => void;
}

export const MessageItem: React.FC<MessageItemProps> = ({ message, onEditMessage, onRegenerateMessage }) => {
  const isUser = message.role === 'user';
  const isAssistant = message.role === 'assistant';

  // 编辑状态
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(message.content);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const bubbleRef = useRef<HTMLDivElement>(null);

  // 计时器状态
  const [elapsedTime, setElapsedTime] = useState(0);

  // 计时器效果
  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;

    if (message.isStreaming) {
      // 重置计时器
      setElapsedTime(0);
      // 启动计时器，每100ms更新一次
      interval = setInterval(() => {
        setElapsedTime(prev => prev + 0.1);
      }, 100);
    } else {
      // 停止计时器
      if (interval) {
        clearInterval(interval);
      }
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [message.isStreaming]);

  // 格式化时间
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // 处理编辑保存
  const handleSaveEdit = () => {
    if (onEditMessage && editContent.trim() !== message.content) {
      onEditMessage(message.id, editContent.trim());
    }
    setIsEditing(false);
  };

  // 处理编辑取消
  const handleCancelEdit = () => {
    setEditContent(message.content);
    setIsEditing(false);
  };

  // 处理重新生成
  const handleRegenerate = () => {
    if (onRegenerateMessage) {
      onRegenerateMessage(message.id);
    }
  };

  // 更新编辑内容当消息内容变化时
  useEffect(() => {
    setEditContent(message.content);
  }, [message.content]);

  // 自动调整textarea尺寸以完全重合气泡
  const autoResizeTextarea = () => {
    if (textareaRef.current && bubbleRef.current) {
      const bubbleRect = bubbleRef.current.getBoundingClientRect();

      // 设置textarea尺寸完全匹配整个气泡
      textareaRef.current.style.width = `${bubbleRect.width}px`;
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${Math.max(textareaRef.current.scrollHeight, bubbleRect.height)}px`;
    }
  };

  // 当编辑内容变化时自动调整尺寸
  useEffect(() => {
    if (isEditing) {
      // 延迟执行以确保DOM已更新
      setTimeout(() => {
        autoResizeTextarea();
      }, 0);
    }
  }, [editContent, isEditing]);

  // 监听窗口大小变化，重新调整textarea尺寸
  useEffect(() => {
    if (isEditing) {
      const handleResize = () => {
        autoResizeTextarea();
      };

      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }
  }, [isEditing]);

  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-6`}>
      <div className={`flex max-w-[92%] ${isUser ? 'flex-row-reverse' : 'flex-row'}`}>
        {/* 头像 */}
        <div className={`flex-shrink-0 ${isUser ? 'ml-3' : 'mr-3'}`}>
          <div className={`w-10 h-10 rounded-2xl flex items-center justify-center shadow-md ${
            isUser
              ? 'bg-gradient-to-br from-[#00C250] to-[#00a843] text-white'
              : 'bg-gradient-to-br from-[#5a9d6b] to-[#65ad79] text-white'
          }`}>
            <span className="material-icons text-lg">
              {isUser ? 'person' : 'smart_toy'}
            </span>
          </div>
        </div>

        {/* 消息内容 */}
        <div className={`flex flex-col ${isUser ? 'items-end' : 'items-start'}`}>
          {/* 消息气泡和计时器的容器 */}
          <div className="flex items-start space-x-2">
            {/* 消息气泡 */}
            <div
              ref={bubbleRef}
              className={`relative px-5 py-4 rounded-2xl max-w-full shadow-sm ${
                isUser
                  ? 'bg-gradient-to-br from-[#00C250] to-[#00a843] text-white rounded-br-lg'
                  : 'bg-white text-gray-800 rounded-bl-lg border border-gray-100'
              }`}
            >
              {/* 流式生成指示器 */}
              {message.isStreaming && (
                <div className="flex items-center space-x-2 mb-3">
                  <div className="flex space-x-1">
                    <div className={`w-2 h-2 rounded-full animate-bounce ${isUser ? 'bg-white bg-opacity-70' : 'bg-[#5a9d6b]'}`}></div>
                    <div className={`w-2 h-2 rounded-full animate-bounce ${isUser ? 'bg-white bg-opacity-70' : 'bg-[#5a9d6b]'}`} style={{ animationDelay: '0.1s' }}></div>
                    <div className={`w-2 h-2 rounded-full animate-bounce ${isUser ? 'bg-white bg-opacity-70' : 'bg-[#5a9d6b]'}`} style={{ animationDelay: '0.2s' }}></div>
                  </div>
                  <span className={`text-xs ${isUser ? 'text-white text-opacity-80' : 'text-gray-500'}`}>
                    AI正在思考...
                  </span>
                </div>
              )}

              {/* 消息文本 */}
              <div className={`whitespace-pre-wrap break-words leading-relaxed ${isEditing ? 'invisible' : ''}`}>
                {message.content || (message.isStreaming ? '' : '消息生成失败')}
              </div>

              {/* 编辑模式的textarea覆盖层 */}
              {isEditing && (
                <textarea
                  value={editContent}
                  onChange={(e) => setEditContent(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && e.ctrlKey) {
                      // Ctrl+Enter 保存
                      e.preventDefault();
                      handleSaveEdit();
                    } else if (e.key === 'Escape') {
                      // Escape 取消
                      e.preventDefault();
                      handleCancelEdit();
                    }
                  }}
                  ref={textareaRef}
                  className="absolute top-0 left-0 bg-transparent border-none resize-none focus:outline-none whitespace-pre-wrap break-words leading-relaxed overflow-hidden"
                  style={{
                    fontFamily: 'inherit',
                    fontSize: 'inherit',
                    lineHeight: 'inherit',
                    color: 'inherit',
                    padding: '20px', // 匹配气泡的 px-5 py-4 (20px 16px)
                    paddingTop: '16px',
                    paddingBottom: '16px',
                    margin: '0',
                    width: '100%',
                    height: '100%',
                    zIndex: 10
                  }}
                  onInput={autoResizeTextarea}
                  autoFocus
                  placeholder="编辑消息内容... (Ctrl+Enter保存, Esc取消)"
                />
              )}

              {/* 流式生成光标 */}
              {message.isStreaming && message.content && (
                <span className={`inline-block w-0.5 h-5 ml-1 animate-pulse ${isUser ? 'bg-white' : 'bg-gray-600'}`}></span>
              )}
            </div>

            {/* 计时器显示在气泡右边 */}
            {!isUser && message.isStreaming && (
              <div className="flex items-center mt-1">
                <div className="bg-gray-100 px-2 py-1 rounded-full text-xs text-gray-600 whitespace-nowrap">
                  {elapsedTime.toFixed(1)}秒
                </div>
              </div>
            )}
          </div>

          {/* 功能按钮区域 - 只对AI助手消息显示 */}
          {isAssistant && !message.isStreaming && (
            <div className={`flex space-x-2 mt-2 ${isUser ? 'justify-end' : 'justify-start'}`}>
              {isEditing ? (
                <>
                  <button
                    onClick={handleCancelEdit}
                    className="flex items-center space-x-1 px-2 py-1 text-xs text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded transition-all duration-200"
                    title="取消编辑"
                  >
                    <span className="material-icons text-sm">close</span>
                    <span>取消</span>
                  </button>
                  <button
                    onClick={handleSaveEdit}
                    className="flex items-center space-x-1 px-2 py-1 text-xs text-white bg-blue-500 hover:bg-blue-600 rounded transition-all duration-200"
                    title="保存编辑"
                  >
                    <span className="material-icons text-sm">check</span>
                    <span>保存</span>
                  </button>
                </>
              ) : (
                <>
                  <button
                    onClick={() => setIsEditing(true)}
                    className="flex items-center space-x-1 px-2 py-1 text-xs text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded transition-all duration-200"
                    title="编辑消息"
                  >
                    <span className="material-icons text-sm">edit</span>
                    <span>编辑</span>
                  </button>
                  <button
                    onClick={handleRegenerate}
                    className="flex items-center space-x-1 px-2 py-1 text-xs text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded transition-all duration-200"
                    title="重新生成"
                  >
                    <span className="material-icons text-sm">refresh</span>
                    <span>重新生成</span>
                  </button>
                </>
              )}
            </div>
          )}

          {/* 时间戳 */}
          <div className={`text-xs text-gray-400 mt-2 px-1 ${isUser ? 'text-right' : 'text-left'}`}>
            {formatTime(message.timestamp)}
          </div>
        </div>
      </div>
    </div>
  );
};
