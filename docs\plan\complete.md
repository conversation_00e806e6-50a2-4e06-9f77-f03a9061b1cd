# 烛光写作平台计划 - 2024版

## 一、概述
本系统提供基于不同模型的调用服务。用户通过订阅月度套餐获得基础的每日调用额度，并通过购买具有时效性的通用额度包来增加调用次数。系统通过Key池管理调用资源，并采用动态的额度/次数消耗机制。

用户分级：
- 免费用户：可以每天10条普通模型使用，不可使用高级模型。
- 付费用户：可以使用全部功能。会员未过期的都是付费用户。

## 二、付费订阅模式 (月付制)

1. 订阅等级与每日额度:
   - 所有订阅均为月付。
   - 49元/月: 普通模型: 25次/天, 高级模型: 10次/天
   - 99元/月: 普通模型: 50次/天, 高级模型: 25次/天
   - 189元/月: 普通模型: 100次/天, 高级模型: 50次/天
   - 每日额度重置: 用户的订阅每日额度在服务器时间每日凌晨00:00自动重置。当天未使用的订阅额度不累计到次日。

2. 每日额度耗尽:
   当用户当日对应模型的订阅每日额度使用完毕后，当天将无法再通过订阅额度调用该模型。用户可选择使用通用额度包（如有且次数>0）或等待次日额度刷新。

## 三、通用额度包 (Universal Quota Pack)

1. 购买资格:
   - 仅限付费会员(49/99/189元档用户)可购买
   - 免费用户无法购买

2. 规格与价格:
   - 5元: 购买50次通用额度
   - 10元: 购买100次通用额度
   - 购买说明: 购买的是通用调用次数，可用于调用普通模型或高级模型

3. 有效期:
   - 每个通用额度包从购买成功时刻起计算，有效期为48小时
   - 过期作废: 超过48小时后，无论包内是否还有剩余次数，该额度包都将自动失效作废
   - 重要提示: 必须在购买界面极其醒目地告知用户此48小时有效期限制以及通用额度的消耗规则

4. 通用额度消耗规则:
   - 调用普通模型: 每次消耗1次通用额度
   - 调用高级模型: 每次消耗1次通用额度
   - 注意: 虽然用户层面每次调用仅消耗1次通用额度，但后台Key的模型调用限制仍然是独立的

5. 消耗优先级:
   - 绝对优先消耗订阅额度: 当用户发起调用请求时，系统首先检查并消耗用户的当日订阅额度(对应模型)
   - 其次消耗通用额度包: 只有当当日订阅额度已耗尽时，系统才会检查用户账户中有效的、未过期的通用额度包

6. 通用额度包消耗顺序与条件:
   - 如果用户拥有多个有效、未过期的通用额度包，系统将按照购买时间顺序(FIFO)检查
   - 系统会选择第一个(最早购买的)满足以下条件的通用额度包进行扣减：
     * 状态为有效且未过期
     * 剩余次数>0
   - 如果找不到满足条件的通用额度包，则无法使用通用额度进行调用

7. 退款机制:
   - 无任何退款: 通用额度包一经购买，不提供任何形式的退款或补偿，无论是否使用、剩余多少次数、是否在有效期内或已过期

8. 购买与管理:
   - 用户可通过网页端为其付费账户购买通用额度包
   - 用户可在网页端查看已购买通用额度包的列表，包含剩余次数和精确的到期时间

## 四、Key管理与技术实现

1. Key池 (Key Pool):
   - 系统维护一个API Key池

2. Key自身限额:
   - 每个Key在其动态24小时重置周期内，具有独立的调用次数限制：
     * 高级模型: 25次
     * 普通模型: 500次

3. Key限额重置机制:
   - 动态24小时重置: 基于Key上次成功使用时间点+24小时后重置对应模型的计数

4. 调用流程与额度/次数扣减:
   a) 用户请求: 用户发起调用请求(指定模型类型: 普通/高级)
   b) 用户权限与额度/次数检查(优先级):
      - IF 用户是免费用户:
        * 检查每日10次普通模型额度，拒绝高级模型调用
        * 是：标记为使用【免费额度】，进入Key检查
        * 否：拒绝
      - ELSE (用户是付费用户):
        * 检查用户当日订阅额度(对应模型)是否>已用次数？
        * 是：标记为使用【订阅额度】，进入Key检查
        * 否：检查通用额度包:
          - 查找第一个(FIFO)有效、未过期且剩余次数>0的【通用额度包】
          - 找到：标记为使用【通用额度】(记录包ID)，进入Key检查
          - 未找到：拒绝(订阅额度耗尽且无可用通用额度)

5. Key分配与轮换:
   a) Key分配策略：
      - 用户首次使用时分配Key
      - Key绑定到特定用户后专属使用
      - 系统记录每个Key的最后使用时间
   b) Key轮换规则：
      - 如果某个Key在最后一次使用后超过24小时未被使用：
        * 自动解除与用户的绑定关系
        * 将Key重新放入可分配池
        * 该用户下次使用时会重新分配Key
   c) Key池管理：
      - 系统持续监控Key的使用状态
      - 对于新用户优先分配未绑定的Key
      - 如果所有Key都已绑定且活跃，则等待Key释放

6. 数据追踪:
   - 用户层面: 订阅等级、每日额度/已用、通用额度包列表（含ID、购买时间、到期时间、总次数、剩余次数、状态）、免费用户每日已用次数
   - Key层面: Key标识、各模型限额(25/500)、各模型自上次重置后已用次数、last_used_timestamp

## 五、用户界面与管理

1. 网页端功能:
   - 登录、账户管理、订阅管理
   - 查看当前订阅状态、每日额度及当日剩余额度
   - 购买通用额度包(需明确告知次数及消耗规则)
   - 查看已购买通用额度包列表，包含剩余次数和精确到期时间
   - （可选）显示总的可用通用额度次数(所有有效包的总和)
   - 消费记录（可选，需区分额度消耗和通用额度消耗）
   - 规则说明展示

## 六、其他

1. 免费用户:
   - 每日10次普通模型调用
   - 无法调用高级模型
   - 无法购买通用额度包

2. 后台任务:
   - 定时检查并更新过期的通用额度包状态
   - 监控Key池