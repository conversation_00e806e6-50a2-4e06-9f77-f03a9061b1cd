/**
 * AI创作助手知识库选择模态窗口组件 - 简化版，仅用于选择知识条目
 */
import React, { useState, useEffect } from 'react';
import { Modal } from '@/components/common/modals';
import { Knowledge } from '@/data';
import { getKnowledgeByWorkId } from '@/data';

interface AIKnowledgeSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (knowledge: Knowledge) => void;
  workId?: number;
  initialSelectedIds?: number[];
}



/**
 * AI创作助手知识库选择模态窗口组件 - 简化版，仅用于选择知识
 */
export const AIKnowledgeSelectionModal: React.FC<AIKnowledgeSelectionModalProps> = ({
  isOpen,
  onClose,
  onSelect,
  workId,
  initialSelectedIds = []
}) => {
  // 状态
  const [knowledgeItems, setKnowledgeItems] = useState<Knowledge[]>([]);

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedKnowledge, setSelectedKnowledge] = useState<Knowledge[]>([]);

  // 加载知识
  const loadKnowledge = async () => {
    if (!workId) return;

    setIsLoading(true);
    setError('');

    try {
      // 按作品ID加载知识
      let knowledgeData = await getKnowledgeByWorkId(workId);



      // 按 updatedAt 降序排序
      knowledgeData.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());

      setKnowledgeItems(knowledgeData);

      // 更新已选择的知识
      if (initialSelectedIds.length > 0) {
        const selectedOnes = knowledgeData.filter(knowledge =>
          knowledge.id !== undefined && initialSelectedIds.includes(knowledge.id)
        );
        setSelectedKnowledge(selectedOnes);
      }

    } catch (error) {
      console.error('加载知识失败:', error);
      setError('加载知识失败，请稍后再试。');
    } finally {
      setIsLoading(false);
    }
  };

  // 当模态窗口打开时加载知识
  useEffect(() => {
    if (isOpen) {
      loadKnowledge();
      // 关闭时重置状态
      return () => {
        setError('');
        setSearchTerm('');
      };
    }
  }, [isOpen, workId]);



  // 处理知识点击
  const handleKnowledgeClick = (knowledge: Knowledge) => {
    // 切换选中状态
    const knowledgeId = knowledge.id;
    if (knowledgeId === undefined) return;

    const index = selectedKnowledge.findIndex(a => a.id === knowledgeId);

    if (index === -1) {
      // 添加到选中列表
      setSelectedKnowledge([...selectedKnowledge, knowledge]);
      onSelect(knowledge); // 通知父组件
    } else {
      // 从选中列表移除
      const newSelectedKnowledge = [...selectedKnowledge];
      newSelectedKnowledge.splice(index, 1);
      setSelectedKnowledge(newSelectedKnowledge);
      onSelect(knowledge); // 通知父组件
    }
  };

  // 确认选择
  const handleConfirm = () => {
    onClose();
  };

  // 过滤知识
  const filteredKnowledge = searchTerm
    ? knowledgeItems.filter(knowledge =>
        knowledge.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        knowledge.content.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : knowledgeItems;



  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="选择知识"
      footer={
        <div className="flex justify-end pt-2">
          <button
            onClick={handleConfirm}
            className="px-4 py-2 bg-[#7D85CC] text-white rounded-lg hover:bg-[#6b73b3] flex items-center"
          >
            <span className="material-icons mr-1 text-sm">check</span>
            确认选择 ({selectedKnowledge.length} 个知识)
          </button>
        </div>
      }
      maxWidth="max-w-2xl"
    >
      <div className="flex h-[70vh]">
        {/* 知识列表 */}
        <div className="w-full flex flex-col">
          {/* 搜索框 */}
          <div className="mb-4">
            <input
              type="text"
              placeholder="搜索知识..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-4 py-2 rounded-lg border border-[rgba(125,133,204,0.3)] focus:outline-none focus:ring-2 focus:ring-[#7D85CC]"
            />
          </div>

          {/* 知识列表 */}
          <div className="flex-1 overflow-y-auto">
            {isLoading ? (
              <div className="h-full flex items-center justify-center">
                <span className="text-[#7D85CC]">加载中...</span>
              </div>
            ) : error ? (
              <div className="h-full flex items-center justify-center">
                <span className="text-red-500">{error}</span>
              </div>
            ) : filteredKnowledge.length === 0 ? (
              <div className="h-full flex items-center justify-center flex-col">
                <span className="material-icons text-4xl text-[rgba(125,133,204,0.3)] mb-2">folder_open</span>
                <span className="text-text-light text-center">
                  {workId !== undefined
                    ? '此作品还没有知识'
                    : '没有找到知识'
                  }
                </span>
              </div>
            ) : (
              <div className="space-y-2">
                {filteredKnowledge.map(knowledge => {
                  const isSelected = selectedKnowledge.some(a => a.id === knowledge.id);

                  return (
                    <div
                      key={knowledge.id}
                      className={`group p-3 rounded-lg transition-colors cursor-pointer flex items-center ${
                        isSelected
                        ? 'bg-[#dfe3f5] border-l-4 border-[#7D85CC]' // 选中项的样式
                        : 'bg-[#ebeef8] hover:bg-[#e4e8f6] border-l-4 border-transparent' // 默认样式
                      }`}
                      onClick={() => handleKnowledgeClick(knowledge)}
                    >
                      {/* 复选框 - 垂直居中 */}
                      <div className="flex items-center h-full">
                        <input
                          type="checkbox"
                          checked={isSelected}
                          readOnly
                          className="mr-3 h-4 w-4 text-[#7D85CC] focus:ring-[#7D85CC] border-gray-300 rounded cursor-pointer"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleKnowledgeClick(knowledge);
                          }}
                        />
                      </div>

                      {/* 知识信息 */}
                      <div className="flex items-center flex-1 min-w-0">


                        <div className="flex-1 min-w-0">
                          {/* 标题 */}
                          <div className="font-medium text-text-dark truncate">
                            {knowledge.title}
                          </div>

                          {/* 内容预览和日期 */}
                          <div className="relative">
                            {/* 内容预览 */}
                            <div className="text-sm text-text-medium mt-1 line-clamp-2 pr-16">
                              {knowledge.content}
                            </div>

                            {/* 更新日期 - 右下角 */}
                            <div className="text-xs text-text-light absolute bottom-0 right-0">
                              {new Date(knowledge.updatedAt).toLocaleDateString()}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </div>
      </div>
    </Modal>
  );
};
