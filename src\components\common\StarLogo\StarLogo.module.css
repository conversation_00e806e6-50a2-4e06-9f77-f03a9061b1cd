/* 星河Logo设计 - 完美还原版 */
.logoIcon {
    width: 48px;
    height: 48px;
    position: relative;
    background: #7D85CC;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow:
        0 6px 20px rgba(125, 133, 204, 0.4),
        inset 0 2px 4px rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.1);
}

.logoStars {
    position: relative;
    width: 40px;
    height: 40px;
}

/* 中心大星 - 金色突出 */
.centerStar {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 10px;
    height: 10px;
    background: linear-gradient(45deg, #ffd700 0%, #ffed4e 50%, #ffc107 100%);
    clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);
    box-shadow:
        0 0 8px #ffd700,
        0 2px 4px #000000;
    filter: drop-shadow(0 0 2px #ffd700);
}

/* 环绕小星 - 纯白高对比 */
.orbitStar {
    position: absolute;
    width: 5px;
    height: 5px;
    background: #ffffff;
    clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);
    box-shadow:
        0 0 4px #ffffff,
        0 1px 2px #000000;
    filter: drop-shadow(0 0 1px #ffffff);
}

/* 8个小星的位置 - 更合理的间距分布 */
.orbitStar:nth-child(1) { top: 3px; left: 50%; transform: translateX(-50%); }
.orbitStar:nth-child(2) { top: 6px; right: 6px; }
.orbitStar:nth-child(3) { top: 50%; right: 3px; transform: translateY(-50%); }
.orbitStar:nth-child(4) { bottom: 6px; right: 6px; }
.orbitStar:nth-child(5) { bottom: 3px; left: 50%; transform: translateX(-50%); }
.orbitStar:nth-child(6) { bottom: 6px; left: 6px; }
.orbitStar:nth-child(7) { top: 50%; left: 3px; transform: translateY(-50%); }
.orbitStar:nth-child(8) { top: 6px; left: 6px; }

/* 自定义尺寸支持 */
.logoIcon[data-size] {
    width: var(--logo-size);
    height: var(--logo-size);
}

.logoIcon[data-size] .logoStars {
    width: calc(var(--logo-size) * 0.833);
    height: calc(var(--logo-size) * 0.833);
}

.logoIcon[data-size] .centerStar {
    width: calc(var(--logo-size) * 0.208);
    height: calc(var(--logo-size) * 0.208);
}

.logoIcon[data-size] .orbitStar {
    width: calc(var(--logo-size) * 0.104);
    height: calc(var(--logo-size) * 0.104);
}


