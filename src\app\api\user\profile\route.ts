/**
 * 用户资料API路由
 * 处理用户资料更新
 */
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Supabase配置
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

/**
 * PUT 方法 - 更新用户资料
 */
export async function PUT(request: NextRequest) {
  try {
    // 从请求头获取Authorization token
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: '未提供认证令牌' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7); // 移除 "Bearer " 前缀

    // 创建带token的客户端实例
    const supabase = createClient(supabaseUrl, supabaseAnonKey, {
      global: {
        headers: {
          Authorization: `Bearer ${token}`
        }
      }
    });

    // 解析请求体
    const body = await request.json();
    const { name, display_name, avatar_url, email } = body;

    // 构建更新数据
    const updateData: any = {};
    
    if (name !== undefined) {
      updateData.data = { ...updateData.data, name };
      // 如果设置了name但没有设置display_name，自动同步
      if (!display_name) {
        updateData.data.display_name = name;
      }
    }
    
    if (display_name !== undefined) {
      updateData.data = { ...updateData.data, display_name };
    }
    
    if (avatar_url !== undefined) {
      updateData.data = { ...updateData.data, avatar_url };
    }
    
    if (email !== undefined) {
      // 验证邮箱格式
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return NextResponse.json(
          { error: '邮箱格式不正确' },
          { status: 400 }
        );
      }
      updateData.email = email;
    }

    // 如果没有任何更新数据
    if (Object.keys(updateData).length === 0) {
      return NextResponse.json(
        { error: '没有提供要更新的数据' },
        { status: 400 }
      );
    }

    console.log('开始更新用户资料:', updateData);

    // 更新用户资料
    const { data, error } = await supabase.auth.updateUser(updateData);

    if (error) {
      console.error('更新用户资料失败:', error);
      return NextResponse.json(
        { error: error.message || '更新用户资料失败' },
        { status: 400 }
      );
    }

    console.log('用户资料更新成功');

    return NextResponse.json({
      success: true,
      message: '用户资料更新成功',
      user: data.user
    });

  } catch (error) {
    console.error('更新用户资料失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}
