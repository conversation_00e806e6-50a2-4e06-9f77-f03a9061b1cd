/**
 * 文件下载API
 * 处理从MinIO下载文件
 */
import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { downloadFile } from '@/lib/minio';

// Supabase配置
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

/**
 * 服务器端获取当前用户
 */
async function getCurrentUser(request: NextRequest) {
  try {
    const supabaseServer = createServerClient(
      supabaseUrl,
      supabaseAnonKey,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll().map(cookie => ({
              name: cookie.name,
              value: cookie.value
            }));
          },
          setAll() {
            // 在API路由中不需要设置cookies
          },
        },
      }
    );

    const { data: { user }, error } = await supabaseServer.auth.getUser();

    if (error) {
      console.error('获取用户失败:', error);
      return null;
    }

    return user;
  } catch (error) {
    console.error('获取用户异常:', error);
    return null;
  }
}

/**
 * GET 方法 - 下载文件
 */
export async function GET(request: NextRequest) {
  try {
    // 获取当前用户
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json(
        { error: '用户未登录' },
        { status: 401 }
      );
    }

    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const fileId = searchParams.get('id');
    const filePath = searchParams.get('path');

    if (!fileId && !filePath) {
      return NextResponse.json(
        { error: '缺少必需参数：id 或 path' },
        { status: 400 }
      );
    }

    // 创建Supabase客户端
    const supabaseServer = createServerClient(
      supabaseUrl,
      supabaseAnonKey,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll().map(cookie => ({
              name: cookie.name,
              value: cookie.value
            }));
          },
          setAll() {},
        },
      }
    );

    let fileRecord;

    if (fileId) {
      // 通过文件ID查询
      const { data, error } = await supabaseServer
        .from('novel_files')
        .select('*')
        .eq('id', fileId)
        .eq('user_id', user.id)
        .single();

      if (error || !data) {
        return NextResponse.json(
          { error: '文件不存在或无权限访问' },
          { status: 404 }
        );
      }

      fileRecord = data;
    } else if (filePath) {
      // 通过文件路径查询
      const { data, error } = await supabaseServer
        .from('novel_files')
        .select('*')
        .eq('minio_object_key', filePath)
        .eq('user_id', user.id)
        .single();

      if (error || !data) {
        return NextResponse.json(
          { error: '文件不存在或无权限访问' },
          { status: 404 }
        );
      }

      fileRecord = data;
    }

    if (!fileRecord) {
      return NextResponse.json(
        { error: '文件记录不存在' },
        { status: 404 }
      );
    }

    console.log(`开始下载文件: ${fileRecord.minio_object_key}`);

    // 从MinIO下载文件
    const fileStream = await downloadFile(fileRecord.minio_object_key);

    // 将流转换为Buffer
    const chunks: Buffer[] = [];
    for await (const chunk of fileStream) {
      chunks.push(chunk);
    }
    const fileBuffer = Buffer.concat(chunks);

    console.log(`文件下载成功: ${fileRecord.minio_object_key}`);

    // 从路径中提取作品标题作为下载文件名
    // 路径格式：xiaoshuo/用户邮箱/作品标题/正文.txt
    const pathParts = fileRecord.minio_object_key.split('/');
    let downloadFileName = fileRecord.file_name; // 默认使用原文件名

    if (pathParts.length >= 3) {
      const workTitle = pathParts[2]; // 获取作品标题部分
      downloadFileName = `${workTitle}.txt`;
      console.log(`提取作品标题作为下载文件名: ${downloadFileName}`);
    }

    // 返回文件内容
    return new NextResponse(fileBuffer, {
      status: 200,
      headers: {
        'Content-Type': fileRecord.mime_type || 'text/plain; charset=utf-8',
        'Content-Disposition': `attachment; filename="${encodeURIComponent(downloadFileName)}"`,
        'Content-Length': fileBuffer.length.toString(),
      },
    });

  } catch (error) {
    console.error('文件下载失败:', error);
    return NextResponse.json(
      { 
        error: '文件下载失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
