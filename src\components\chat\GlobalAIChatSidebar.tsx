'use client';

import React, { useEffect, useState } from 'react';
import { useAIChat } from '@/contexts/AIChatContext';
import { ChatInterface } from './ChatInterface';
import { useAuth } from '@/hooks/useAuth';

export const GlobalAIChatSidebar: React.FC = () => {
  const { isOpen, closeChat } = useAIChat();
  const { isAuthenticated } = useAuth();
  const [isMounted, setIsMounted] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // 处理动画状态
  useEffect(() => {
    if (isOpen) {
      setIsAnimating(true);
    } else {
      // 延迟隐藏，等待动画完成
      const timer = setTimeout(() => {
        setIsAnimating(false);
      }, 350); // 与动画时长一致
      return () => clearTimeout(timer);
    }
  }, [isOpen]);

  useEffect(() => {
    // 当侧边栏打开时，禁止body滚动
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    // 清理函数
    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  // 处理ESC键关闭
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        closeChat();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, closeChat]);

  if (!isMounted || (!isOpen && !isAnimating)) return null;

  return (
    <div className="ai-chat-sidebar-container">
      {/* 背景遮罩 */}
      <div
        className={`fixed inset-0 bg-black z-[9998] ${
          isOpen ? 'ai-chat-backdrop-fade-in' : 'ai-chat-backdrop-fade-out'
        }`}
        onClick={closeChat}
      />

      {/* 右侧侧边栏 - A4大小 */}
      <div className={`fixed top-0 right-0 h-full w-[768px] max-w-[95vw] bg-white shadow-2xl z-[9999] flex flex-col rounded-l-2xl overflow-hidden ${
        isOpen ? 'ai-chat-sidebar-slide-in' : 'ai-chat-sidebar-slide-out'
      }`}>
        {/* 顶部栏 */}
        <div className="flex items-center justify-between p-6 bg-gradient-to-r from-[#5a9d6b] to-[#65ad79] relative">
          {/* 装饰性背景图案 */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-2 left-4 w-3 h-3 bg-white rounded-full"></div>
            <div className="absolute top-6 right-8 w-2 h-2 bg-white rounded-full"></div>
            <div className="absolute bottom-3 left-12 w-1.5 h-1.5 bg-white rounded-full"></div>
          </div>

          <div className="flex items-center relative z-10">
            <div className="w-10 h-10 rounded-full bg-white flex items-center justify-center mr-4 shadow-sm">
              <span className="material-icons text-[#5a9d6b] text-xl">chat</span>
            </div>
            <div className="flex flex-col">
              <h2 className="text-xl font-medium text-white" style={{fontFamily: "'Ma Shan Zheng', cursive"}}>
                对话操作助手
              </h2>
              <p className="text-sm text-white text-opacity-80 mt-1">
                对话即操作
              </p>
            </div>
          </div>
          <button
            onClick={closeChat}
            className="w-10 h-10 rounded-full bg-white flex items-center justify-center text-[#5a9d6b] hover:bg-gray-100 transition-all duration-200 shadow-sm hover:scale-105 relative z-10"
          >
            <span className="material-icons">close</span>
          </button>
        </div>

        {/* 聊天界面容器 */}
        <div className="flex-1 overflow-hidden bg-gradient-to-b from-gray-50 to-white">
          {isAuthenticated ? (
            <div className="h-full">
              <ChatInterface />
            </div>
          ) : (
            <div className="flex-1 flex items-center justify-center p-8 h-full">
              <div className="text-center max-w-sm">
                {/* 装饰性图标容器 */}
                <div className="relative mb-6">
                  <div className="w-20 h-20 mx-auto rounded-2xl bg-gradient-to-br from-[#5a9d6b] to-[#65ad79] flex items-center justify-center text-white shadow-lg">
                    <span className="material-icons text-3xl">chat</span>
                  </div>
                  {/* 装饰性小圆点 */}
                  <div className="absolute -top-1 -right-1 w-6 h-6 bg-[#00C250] rounded-full flex items-center justify-center">
                    <span className="material-icons text-white text-sm">auto_awesome</span>
                  </div>
                </div>

                <h3 className="text-2xl font-medium text-text-dark mb-3" style={{fontFamily: "'Ma Shan Zheng', cursive"}}>
                  对话操作助手
                </h3>
                <p className="text-text-medium mb-6 leading-relaxed">
                  请登录后开始对话操作<br/>
                  对话即操作，智能化处理
                </p>
                <button
                  onClick={() => window.location.href = '/login'}
                  className="px-8 py-3 bg-gradient-to-r from-[#00C250] to-[#00a843] text-white rounded-xl hover:shadow-lg transform hover:scale-105 transition-all duration-200 font-medium"
                >
                  立即登录
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
