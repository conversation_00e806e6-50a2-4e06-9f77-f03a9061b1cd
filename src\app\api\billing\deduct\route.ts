/**
 * 专用计费API端点
 * 只允许服务器端内部调用，用于扣除用户字数余额
 * ⚠️ 此API不允许前端直接访问
 */
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// 硬编码内部API密钥 - 只有服务器端代码知道此密钥
const INTERNAL_API_KEY = "billing_internal_2024_secure_key_xyz789";

// 创建Supabase客户端
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

// 计费结果接口
interface BillingResult {
  success: boolean;
  message?: string;
}

// 请求体接口
interface DeductRequest {
  internalApiKey: string;
  userUuid: string;
  amount: number;
  modelCode?: string; // 模型代号，用于判断是否为免费版模型
}

/**
 * ⚠️ 已删除免费模型机制
 * 所有模型现在都需要付费使用
 */
const isFreeModel = (modelCode?: string): boolean => {
  return false; // 所有模型都需要付费
};

/**
 * 执行扣费操作（支持免费额度）
 */
const executeDeduction = async (
  userUuid: string,
  amount: number,
  modelCode?: string
): Promise<BillingResult> => {
  try {
    console.log(`[Billing API] 开始扣费 - 用户: ${userUuid}, 模型: ${modelCode}, 金额: ${amount}`);

    // 检查是否为ceshi模型且用户有免费额度
    if (modelCode === 'ceshi') {
      console.log(`[Billing API] 检测到ceshi模型，尝试使用免费额度`);

      // 尝试扣除免费额度
      const { data: freeDeductResult, error: freeError } = await supabase
        .rpc('deduct_daily_free_quota', {
          user_uuid: userUuid,
          deduct_amount: 1 // ceshi模型每次使用扣除1次免费额度
        });

      console.log(`[Billing API] 免费额度扣除结果 - data: ${freeDeductResult}, error: ${freeError?.message || 'none'}`);

      if (!freeError && freeDeductResult) {
        console.log(`[Billing API] ceshi模型使用免费额度成功`);
        return {
          success: true,
          message: '使用免费额度成功'
        };
      } else {
        console.log(`[Billing API] 免费额度不足或扣除失败，转为付费扣费`);
      }
    } else {
      console.log(`[Billing API] 非ceshi模型 (${modelCode})，直接使用付费扣费`);
    }

    // 免费额度不足或非ceshi模型，使用付费扣费
    console.log(`[Billing API] 模型 ${modelCode}：执行付费扣费`);
    const { data, error } = await supabase
      .rpc('deduct_user_word_count', {
        user_uuid: userUuid,
        deduct_amount: amount
      });

    if (error || !data) {
      return {
        success: false,
        message: `扣费失败: ${error?.message || '未知错误'}`
      };
    }

    return {
      success: true,
      message: '付费扣费成功'
    };
  } catch (error) {
    console.error('扣费异常:', error);
    return {
      success: false,
      message: '扣费异常'
    };
  }
};

/**
 * POST 方法 - 处理扣费请求
 * 只允许服务器端内部调用
 */
export async function POST(request: NextRequest) {
  try {
    // 解析请求体
    const body: DeductRequest = await request.json();
    const { internalApiKey, userUuid, amount, modelCode } = body;

    // 唯一的安全验证：内部API密钥验证（后端token验证）
    if (!internalApiKey || internalApiKey !== INTERNAL_API_KEY) {
      console.error('[Billing API] 无效的内部API密钥访问尝试');
      return NextResponse.json(
        { error: '无效的内部API密钥' },
        { status: 403 }
      );
    }

    // 基本参数验证
    if (!userUuid || typeof amount !== 'number' || amount <= 0) {
      console.error('[Billing API] 无效的请求参数');
      return NextResponse.json(
        { error: '无效的请求参数' },
        { status: 400 }
      );
    }

    console.log(`[Billing API] 开始处理扣费请求 - 用户: ${userUuid}, 金额: ${amount}, 模型: ${modelCode || '未指定'}`);

    // 执行扣费操作
    const result = await executeDeduction(userUuid, amount, modelCode);

    if (result.success) {
      console.log(`[Billing API] 扣费成功`);
    } else {
      console.error(`[Billing API] 扣费失败 - ${result.message}`);
    }

    return NextResponse.json(result);

  } catch (error) {
    console.error('[Billing API] 服务器错误:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

/**
 * 其他HTTP方法不被支持
 */
export async function GET() {
  return NextResponse.json(
    { error: '此端点不支持GET请求' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: '此端点不支持PUT请求' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: '此端点不支持DELETE请求' },
    { status: 405 }
  );
}
