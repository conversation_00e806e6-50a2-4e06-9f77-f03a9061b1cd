{"name": "zhuguang1.0", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "deploy": "wrangler pages deploy .next --project-name=zgzx-tst"}, "dependencies": {"@fontsource/comfortaa": "^5.2.5", "@fontsource/dancing-script": "^5.2.5", "@fontsource/pacifico": "^5.2.5", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "@tailwindcss/typography": "^0.5.16", "@tiptap/extension-character-count": "^2.11.7", "@tiptap/extension-image": "^2.11.7", "@tiptap/pm": "^2.11.7", "@tiptap/react": "^2.11.7", "@tiptap/starter-kit": "^2.11.7", "@types/minio": "^7.1.0", "dotenv": "^16.5.0", "encoding-japanese": "^2.2.0", "framer-motion": "^12.7.4", "fs-extra": "^11.3.0", "glob": "^11.0.1", "gpt-tokenizer": "^3.0.0", "idb": "^8.0.2", "jschardet": "^3.1.4", "minio": "^8.0.5", "next": "14.2.5", "openai": "^4.95.1", "react": "^18", "react-dom": "^18", "react-markdown": "^10.1.0", "text-encoding": "^0.7.0", "zustand": "^5.0.3"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.21", "eslint": "^8", "eslint-config-next": "14.2.5", "postcss": "^8.5.3", "postcss-import": "^16.1.0", "tailwind-scrollbar": "^3.1.0", "tailwindcss": "^3.4.17", "typescript": "^5"}}