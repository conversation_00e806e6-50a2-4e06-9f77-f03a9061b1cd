/**
 * 下拉菜单组件 - 吉卜力风格
 */
import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';

export interface DropdownMenuItem {
  id: string;
  label: string;
  icon?: string;
  onClick: () => void;
  className?: string;
  disabled?: boolean;
}

interface DropdownMenuProps {
  trigger: React.ReactNode;
  items: DropdownMenuItem[];
  className?: string;
  menuClassName?: string;
  align?: 'left' | 'right';
}

/**
 * 下拉菜单组件
 * @param props 下拉菜单属性
 * @returns 下拉菜单组件
 */
export const DropdownMenu: React.FC<DropdownMenuProps> = ({
  trigger,
  items,
  className = '',
  menuClassName = '',
  align = 'right'
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [menuPosition, setMenuPosition] = useState({ top: 0, left: 0 });
  const triggerRef = useRef<HTMLDivElement>(null);
  const menuRef = useRef<HTMLDivElement>(null);

  // 计算菜单位置
  const calculateMenuPosition = () => {
    if (!triggerRef.current) return;

    const triggerRect = triggerRef.current.getBoundingClientRect();
    const menuWidth = 140; // 增大宽度
    const menuHeight = items.length * 48; // 增大高度估算 (每项48px)

    let top = triggerRect.bottom + 8; // 触发器下方8px，增加间距避免误触
    let left = align === 'right'
      ? triggerRect.right - menuWidth
      : triggerRect.left;

    // 确保菜单不会超出视窗
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // 水平边界检查
    if (left + menuWidth > viewportWidth) {
      left = viewportWidth - menuWidth - 12; // 增加边距
    }
    if (left < 12) {
      left = 12; // 增加边距
    }

    // 垂直边界检查
    if (top + menuHeight > viewportHeight) {
      top = triggerRect.top - menuHeight - 8; // 显示在触发器上方，增加间距
    }

    setMenuPosition({ top, left });
  };

  // 点击外部关闭菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        triggerRef.current &&
        !triggerRef.current.contains(event.target as Node) &&
        menuRef.current &&
        !menuRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      calculateMenuPosition();
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, items.length, align]);

  // 处理触发器点击
  const handleTriggerClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsOpen(!isOpen);
  };

  // 处理菜单项点击
  const handleItemClick = (item: DropdownMenuItem, e: React.MouseEvent) => {
    e.stopPropagation();
    if (!item.disabled) {
      item.onClick();
      setIsOpen(false);
    }
  };

  return (
    <>
      {/* 触发器 */}
      <div className={`relative ${className}`} ref={triggerRef} onClick={handleTriggerClick}>
        {trigger}
      </div>

      {/* 下拉菜单 - 使用 Portal 渲染到 body */}
      {isOpen && typeof window !== 'undefined' && createPortal(
        <div
          ref={menuRef}
          className={`
            fixed z-[99999] min-w-[140px]
            bg-card-color border border-[rgba(120,180,140,0.3)]
            rounded-xl shadow-xl overflow-hidden
            animate-fadeInFast
            ${menuClassName}
          `}
          style={{
            top: `${menuPosition.top}px`,
            left: `${menuPosition.left}px`,
          }}
        >
          {items.map((item) => (
            <button
              key={item.id}
              className={`
                w-full px-4 py-3 text-left text-sm
                flex items-center space-x-3
                hover:bg-[rgba(120,180,140,0.1)]
                transition-colors duration-200
                ${item.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                ${item.className || ''}
              `}
              onClick={(e) => handleItemClick(item, e)}
              disabled={item.disabled}
            >
              {item.icon && (
                <span className="material-icons text-base">{item.icon}</span>
              )}
              <span className="font-medium">{item.label}</span>
            </button>
          ))}
        </div>,
        document.body
      )}
    </>
  );
};
