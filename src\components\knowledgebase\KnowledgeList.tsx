import React, { useState } from 'react';
import { Knowledge } from '@/data';

interface KnowledgeListProps {
  knowledgeItems: Knowledge[];
  selectedKnowledge: Knowledge | null;
  isLoading: boolean;
  selectedWorkId: number | null;
  onKnowledgeSelect: (knowledge: Knowledge) => void;
  onCreateKnowledge: () => void;
  onEditTitle?: (knowledge: Knowledge) => void;
  onDeleteKnowledge?: (knowledge: Knowledge) => void;
}

const KnowledgeList: React.FC<KnowledgeListProps> = ({
  knowledgeItems,
  selectedKnowledge,
  isLoading,
  selectedWorkId,
  onKnowledgeSelect,
  onCreateKnowledge,
  onEditTitle,
  onDeleteKnowledge
}) => {
  // 添加编辑状态
  const [editingId, setEditingId] = useState<number | null>(null);
  const [editingTitle, setEditingTitle] = useState<string>('');

  // 处理开始编辑
  const handleStartEdit = (knowledge: Knowledge, e: React.MouseEvent) => {
    e.stopPropagation();
    if (knowledge.id) {
      setEditingId(knowledge.id);
      setEditingTitle(knowledge.title);
    }
  };

  // 处理保存编辑
  const handleSaveEdit = (knowledge: Knowledge) => {
    if (onEditTitle) {
      // 检查标题是否为空，为空则使用默认名称
      const finalTitle = editingTitle.trim() === '' ? '新知识' : editingTitle.trim();
      const updatedKnowledge = { ...knowledge, title: finalTitle, updatedAt: new Date() };
      onEditTitle(updatedKnowledge);
    }
    setEditingId(null);
  };

  // 处理取消编辑
  const handleCancelEdit = () => {
    setEditingId(null);
  };

  // 处理失去焦点时的保存
  const handleBlurEdit = (knowledge: Knowledge) => {
    // 检查标题是否为空，为空则使用默认名称
    const finalTitle = editingTitle.trim() === '' ? '新知识' : editingTitle.trim();

    // 只有在标题确实更改过或从默认空变成默认名时才保存
    if (finalTitle !== knowledge.title && onEditTitle) {
      const updatedKnowledge = { ...knowledge, title: finalTitle, updatedAt: new Date() };
      onEditTitle(updatedKnowledge);
    }

    setEditingId(null);
  };
  return (
    <div className="flex-1 overflow-y-auto px-4 py-2">
      {isLoading ? (
        <div className="flex items-center justify-center h-32">
          <span className="text-[#7D85CC]">加载中...</span>
        </div>
      ) : knowledgeItems.length === 0 ? (
        <div className="flex items-center justify-center h-64 flex-col">
          <div className="w-20 h-20 bg-[rgba(125,133,204,0.1)] rounded-full flex items-center justify-center mb-4">
            <span className="material-icons text-4xl text-[rgba(125,133,204,0.3)]">folder_open</span>
          </div>
          <h3 className="text-lg font-medium text-text-dark mb-2 font-ma-shan">
            {selectedWorkId === null ? "知识库空空如也" : "该作品还没有知识"}
          </h3>
          <p className="text-text-medium text-center max-w-xs mb-6">
            {selectedWorkId === null
              ? "创建你的第一个知识，开始记录创作灵感和素材"
              : "为这个作品创建知识，记录角色、世界观和情节"}
          </p>
          <button
            className={`px-4 py-2 ${
              selectedWorkId === null
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-[#7D85CC] text-white hover:bg-[#6b73b3]'
            } rounded-full flex items-center text-sm shadow-sm`}
            onClick={onCreateKnowledge}
            disabled={selectedWorkId === null}
          >
            <span className="material-icons text-sm mr-1">add</span>
            新建知识
          </button>

        </div>
      ) : (
        <div className="space-y-2">
          {knowledgeItems.map((knowledge) => (
            <div
              key={knowledge.id}
              className={`group p-3 rounded-lg cursor-pointer transition-colors flex items-center ${
                selectedKnowledge?.id === knowledge.id
                  ? 'bg-[#d8ddf2] border-l-4 border-[#7D85CC] shadow-sm'
                  : 'bg-[#ebeef8] hover:bg-[#e4e8f6] border-l-4 border-transparent'
              }`}
              onClick={() => onKnowledgeSelect(knowledge)}
            >
              {/* 分类图标已移至标题行内 */}

              <div className="flex-1 min-w-0">
                <div className="flex justify-between items-center">
                  {editingId === knowledge.id ? (
                    // 编辑模式 - 与作品编辑器知识库一致的样式
                    <input
                      type="text"
                      value={editingTitle}
                      onChange={(e) => setEditingTitle(e.target.value)}
                      className="flex-1 px-2 py-1 text-base font-medium rounded border-0 bg-white shadow-sm focus:outline-none focus:ring-1 focus:ring-[#7D85CC]"
                      autoFocus
                      onClick={(e) => e.stopPropagation()}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          handleSaveEdit(knowledge);
                        } else if (e.key === 'Escape') {
                          handleCancelEdit();
                        }
                      }}
                      onBlur={() => handleBlurEdit(knowledge)}
                    />
                  ) : (
                    // 显示模式
                    <>
                      <div className="flex items-center flex-1 overflow-hidden">
                        <div className="font-medium truncate text-base">
                          {knowledge.title || '无标题知识'}
                        </div>
                      </div>

                      {/* 编辑和删除按钮 - 悬停时显示 */}
                      <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity ml-2 flex-shrink-0">
                        <button
                          onClick={(e) => handleStartEdit(knowledge, e)}
                          className="p-1 rounded-full hover:bg-[rgba(125,133,204,0.2)] text-[#7D85CC]"
                          title="编辑标题"
                        >
                          <span className="material-icons text-sm">edit</span>
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            if (onDeleteKnowledge) onDeleteKnowledge(knowledge);
                          }}
                          className="p-1 rounded-full hover:bg-[rgba(255,100,100,0.2)] text-red-500"
                          title="删除"
                        >
                          <span className="material-icons text-sm">delete</span>
                        </button>
                      </div>
                    </>
                  )}
                </div>

                {/* 内容预览和日期 - 仅在非编辑模式下显示 */}
                {editingId !== knowledge.id && (
                  <div className="relative">
                    <div className="text-sm text-text-medium mt-1 line-clamp-2 pr-16">
                      {knowledge.content || '暂无内容'}
                    </div>

                    {/* 更新日期 - 右下角 */}
                    <div className="text-xs text-text-light absolute bottom-0 right-0">
                      {new Date(knowledge.updatedAt).toLocaleDateString()}
                    </div>
                  </div>
                )}

                {/* 标签 - 如果需要显示标签，可以取消注释这段代码
                <div className="flex flex-wrap gap-1 mt-2">
                  {knowledge.tags && knowledge.tags.slice(0, 2).map((tag, index) => (
                    <span key={index} className="text-xs px-1.5 py-0.5 bg-[rgba(125,133,204,0.1)] text-[#7D85CC] rounded-full">
                      {tag}
                    </span>
                  ))}
                  {knowledge.tags && knowledge.tags.length > 2 && (
                    <span className="text-xs text-text-light">+{knowledge.tags.length - 2}</span>
                  )}
                </div>
                */}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default KnowledgeList;
