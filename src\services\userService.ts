/**
 * 用户服务
 * 使用Supabase标准认证系统
 */
import { createClient } from '@/utils/supabase/client'
import { handleSupabaseAuthError } from '@/lib/utils/ErrorHandler'
import type { User, Session } from '@supabase/supabase-js'

// 导出Supabase类型
export type { User, Session }

// 创建Supabase客户端实例
const supabase = createClient()

/**
 * 创建带认证头的请求选项
 */
const createAuthHeaders = async (): Promise<HeadersInit> => {
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
  };

  // 从Supabase session获取token
  const { data: { session } } = await supabase.auth.getSession();
  if (session?.access_token) {
    headers['Authorization'] = `Bearer ${session.access_token}`;
  }

  return headers;
};

/**
 * 获取当前登录用户
 * @returns 当前登录用户或null
 */
export const getCurrentUser = async (): Promise<User | null> => {
  try {
    const { data: { user }, error } = await supabase.auth.getUser();

    if (error) {
      console.error('获取当前用户失败:', error);
      return null;
    }

    return user;
  } catch (error) {
    console.error('获取当前用户异常:', error);
    return null;
  }
};

/**
 * 用户登录
 * @param email 邮箱
 * @param password 密码
 * @returns 登录结果
 */
export const signIn = async (email: string, password: string) => {
  try {
    // 使用Supabase直接登录
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      console.error('登录失败:', error);

      // 使用统一错误处理
      const errorMessage = handleSupabaseAuthError(error);
      throw new Error(errorMessage);
    }

    if (!data.user || !data.session) {
      throw new Error('登录失败，请重试');
    }

    return {
      user: data.user,
      session: data.session
    };
  } catch (error) {
    console.error('登录失败:', error);
    throw error;
  }
};

/**
 * 用户注册
 * @param email 邮箱
 * @param password 密码
 * @param userId 用户ID（显示名称）
 * @returns 注册结果
 */
export const signUp = async (email: string, password: string, userId: string) => {
  try {
    // 使用Supabase直接注册（OTP模式）
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          name: userId,
          display_name: userId,
        }
        // 移除 emailRedirectTo，使用OTP验证模式
      }
    });

    if (error) {
      console.error('注册失败:', error);
      throw new Error(handleSupabaseAuthError(error));
    }

    return {
      user: data.user,
      session: data.session,
      needsEmailConfirmation: !data.session // 如果没有session，说明需要邮箱确认
    };
  } catch (error) {
    console.error('注册失败:', error);
    throw error;
  }
};

/**
 * 验证邮箱OTP
 */
export const verifyEmailOtp = async (email: string, token: string) => {
  try {
    const { data, error } = await supabase.auth.verifyOtp({
      email,
      token,
      type: 'email'
    });

    if (error) {
      console.error('OTP验证失败:', error);
      throw new Error(handleSupabaseAuthError(error));
    }

    return {
      user: data.user,
      session: data.session
    };
  } catch (error) {
    console.error('OTP验证失败:', error);
    throw error;
  }
};

/**
 * 重新发送OTP验证码
 */
export const resendOtp = async (email: string) => {
  try {
    const { error } = await supabase.auth.resend({
      type: 'signup',
      email: email
    });

    if (error) {
      console.error('重新发送OTP失败:', error);
      throw new Error(handleSupabaseAuthError(error));
    }

    return { success: true };
  } catch (error) {
    console.error('重新发送OTP失败:', error);
    throw error;
  }
};

/**
 * 发送密码重置OTP
 */
export const sendPasswordResetOtp = async (email: string) => {
  try {
    const { error } = await supabase.auth.resetPasswordForEmail(email);

    if (error) {
      console.error('发送密码重置OTP失败:', error);
      throw new Error(error.message || '发送密码重置验证码失败');
    }

    return { success: true };
  } catch (error) {
    console.error('发送密码重置OTP失败:', error);
    throw error;
  }
};

/**
 * 验证密码重置OTP并重置密码
 */
export const verifyPasswordResetOtp = async (email: string, token: string, password: string) => {
  try {
    // 验证OTP
    const { data, error } = await supabase.auth.verifyOtp({
      email,
      token,
      type: 'recovery'
    });

    if (error) {
      console.error('密码重置OTP验证失败:', error);
      throw new Error(handleSupabaseAuthError(error));
    }

    if (!data.user || !data.session) {
      throw new Error('验证失败，请重试');
    }

    // 更新密码
    const { error: updateError } = await supabase.auth.updateUser({
      password: password
    });

    if (updateError) {
      console.error('更新密码失败:', updateError);
      throw new Error(handleSupabaseAuthError(updateError));
    }

    return {
      user: data.user,
      session: data.session
    };
  } catch (error) {
    console.error('密码重置失败:', error);
    throw error;
  }
};

/**
 * 用户登出
 */
export const signOut = async () => {
  try {
    const { error } = await supabase.auth.signOut();

    if (error) {
      console.error('登出失败:', error);
      throw error;
    }
  } catch (error) {
    console.error('登出失败:', error);
    throw error;
  }
};

/**
 * 更新用户资料
 * @param userData 用户资料
 * @returns 更新后的用户
 */
export const updateUserProfile = async (userData: { name?: string; display_name?: string; avatar_url?: string; email?: string }) => {
  try {
    const { data, error } = await supabase.auth.updateUser({
      email: userData.email,
      data: {
        name: userData.name,
        display_name: userData.display_name,
        avatar_url: userData.avatar_url,
      }
    });

    if (error) {
      console.error('更新用户资料失败:', error);
      throw new Error(error.message || '更新用户资料失败');
    }

    return data.user;
  } catch (error) {
    console.error('更新用户资料失败:', error);
    throw error;
  }
};


