/**
 * 前端计费服务模块
 * 只处理用户字数余额查询等前端操作，不包含扣费功能
 * 扣费功能只在后端API中使用
 */
import { createClient } from '@/utils/supabase/client';

// 创建Supabase客户端实例
const supabase = createClient();

// 会员信息接口
export interface MembershipInfo {
  is_active: boolean;
  level: string;
  days_remaining: number | null;
  word_limit: number | null;
  word_used: number;
  word_remaining: number | null;
}

// 计费结果接口
export interface BillingResult {
  success: boolean;
  message?: string;
  remainingBalance?: number;
}

/**
 * 前端计费服务类
 * 只包含查询功能，不包含扣费功能
 */
export class BillingService {
  /**
   * 检查用户会员状态（使用Supabase认证查询）
   * @param userUuid 用户UUID
   * @returns 会员信息
   */
  static async getMembershipInfo(userUuid: string): Promise<MembershipInfo | null> {
    try {
      console.log('开始获取会员信息，用户ID:', userUuid);

      // 直接使用Supabase客户端查询membership-look表
      const { data, error } = await supabase
        .from('membership-look')
        .select('*')
        .eq('user_id', userUuid)
        .single();

      if (error) {
        console.error('获取会员信息失败:', error);
        console.error('错误详情:', {
          code: error.code,
          message: error.message,
          details: error.details,
          hint: error.hint
        });
        return null;
      }

      if (!data) {
        console.log('未找到用户会员数据');
        return null;
      }

      console.log('成功获取会员数据:', data);

      // 转换为标准格式
      const wordRemaining = data.word_count_limit ?
        Math.max(0, data.word_count_limit - (data.word_count_used || 0)) : null;

      const membershipInfo = {
        is_active: data.membership_level !== '免费',
        level: data.membership_level,
        days_remaining: null, // 暂不支持天数计算
        word_limit: data.word_count_limit,
        word_used: data.word_count_used || 0,
        word_remaining: wordRemaining
      } as MembershipInfo;

      console.log('转换后的会员信息:', membershipInfo);
      return membershipInfo;
    } catch (error) {
      console.error('获取会员信息异常:', error);
      return null;
    }
  }

  /**
   * 检查用户字数余额（使用Supabase认证查询）
   * @param userUuid 用户UUID
   * @returns 余额数量，-1表示用户不存在，-2表示无限制，>=0表示剩余字数
   */
  static async checkBalance(userUuid: string): Promise<number> {
    try {
      console.log('开始检查用户余额，用户ID:', userUuid);

      // 直接使用Supabase客户端查询membership-look表
      const { data, error } = await supabase
        .from('membership-look')
        .select('word_count_used, word_count_limit, membership_level')
        .eq('user_id', userUuid)
        .single();

      if (error) {
        console.error('检查余额失败:', error);
        console.error('错误详情:', {
          code: error.code,
          message: error.message,
          details: error.details,
          hint: error.hint
        });
        return -1;
      }

      if (!data) {
        console.log('未找到用户余额数据');
        return -1;
      }

      console.log('成功获取余额数据:', data);

      // 免费用户禁止使用
      if (data.membership_level === '免费') {
        console.log('用户为免费用户，禁止使用AI功能');
        return -2;
      }

      // 计算剩余余额
      const used = data.word_count_used || 0;
      const limit = data.word_count_limit || 0;
      const remaining = Math.max(0, limit - used);

      console.log(`余额计算结果: 已用${used}字, 限制${limit}字, 剩余${remaining}字`);
      return remaining;
    } catch (error) {
      console.error('检查余额异常:', error);
      return -1;
    }
  }

  /**
   * ⚠️ 前端不支持扣费功能
   * 扣费功能只能在后端API中使用
   * @deprecated 前端不应该调用此方法
   */
  static async deductWordCount(userUuid: string, amount: number): Promise<BillingResult> {
    console.error('❌ 前端不允许直接扣费！扣费功能只能在后端API中使用');
    return {
      success: false,
      message: '前端不允许直接扣费，请通过后端API执行'
    };
  }

  // 模型计费倍率配置表
  private static MODEL_BILLING_RATES = {
    'ceshi': { // 测试模型
      inputRate: 5,   // 输入倍率
      outputRate: 40  // 输出倍率
    },
    'kelaode': { // 克劳德模型
      inputRate: 10,  // 输入倍率
      outputRate: 55  // 输出倍率
    }
  };

  /**
   * 计算Token消耗的字数（新倍率计算方式）
   * @param promptTokens 输入token数
   * @param totalTokens 总token数
   * @param modelCode 模型代号
   * @returns 总消耗字数
   */
  static calculateTokenCost(promptTokens: number, totalTokens: number, modelCode: string): number {
    // 输出token = 总token - 输入token
    const outputTokens = totalTokens - promptTokens;

    // 获取模型倍率配置，如果模型不存在则使用测试模型的倍率
    const rates = this.MODEL_BILLING_RATES[modelCode as keyof typeof this.MODEL_BILLING_RATES] || this.MODEL_BILLING_RATES['ceshi'];

    // 修正后的倍率计算：输入token * 输入倍率 + 输出token * 输出倍率
    const inputCost = promptTokens * rates.inputRate;
    const outputCost = outputTokens * rates.outputRate;
    return inputCost + outputCost;
  }

  /**
   * 检查用户是否可以使用AI功能
   * @param userUuid 用户UUID
   * @param estimatedCost 预估消耗（可选）
   * @param modelCode 模型代号（可选，用于检查模型权限）
   * @returns 是否可以使用
   */
  static async canUseAI(userUuid: string, estimatedCost: number = 0, modelCode?: string): Promise<{
    canUse: boolean;
    message?: string;
    balance?: number;
  }> {
    try {
      const balance = await this.checkBalance(userUuid);

      if (balance === -1) {
        return {
          canUse: false,
          message: '用户信息获取失败'
        };
      }

      // 删除免费机制：所有用户都需要付费使用

      // 检查余额是否足够
      if (balance < estimatedCost) {
        return {
          canUse: false,
          message: `字数余额不足，当前余额：${balance}字`,
          balance: balance
        };
      }

      return {
        canUse: true,
        balance: balance
      };
    } catch (error) {
      console.error('检查AI使用权限异常:', error);
      return {
        canUse: false,
        message: '检查权限失败'
      };
    }
  }

  /**
   * ⚠️ 已删除免费模型机制
   * 所有模型现在都需要付费使用
   * @deprecated 免费模型机制已删除
   */
  static isFreeModel(modelCode: string): boolean {
    return false; // 所有模型都需要付费
  }

  /**
   * ⚠️ 前端不支持创建用户记录
   * 用户记录创建应该在注册时由后端处理
   * @deprecated 前端不应该调用此方法
   */
  static async createFreeUser(userUuid: string, userEmail?: string): Promise<boolean> {
    console.error('❌ 前端不允许直接创建用户记录！应该由后端API处理');
    return false;
  }
}

// 导出默认实例
export default BillingService;
