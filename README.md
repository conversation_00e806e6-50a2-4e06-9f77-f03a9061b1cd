# 烛光写作

烛光写作是一个使用Next.js构建的在线写作平台，结合现代UI设计和AI功能，帮助作者创作、整理和优化他们的作品。

## 功能特点

- 美观的用户界面，灵感来源于吉卜力工作室的艺术风格
- 作品管理和编辑器
- 章节管理系统
- AI辅助写作和润色功能
- 知识库系统用于管理创作素材和设定
- 提示词库管理
- 自动保存功能

## 技术栈

- Next.js 14
- React 18
- TypeScript
- Tailwind CSS
- IndexedDB (通过idb库实现本地存储)
- Supabase (用户认证和云端数据存储)
- Zustand (状态管理)
- Gemini API (AI功能)

## 知识库功能

知识库系统允许用户创建和管理创作素材、角色设定、世界观等资料，主要特点：

- 创建、编辑和分类知识条目
- 按分类和标签筛选知识条目
- 将知识条目内容直接引用到写作内容中
- 在AI写作时关联知识条目，使AI更好理解创作背景

## 项目结构

```
/src
  /app - 页面组件
  /components - 可复用UI组件
  /lib - 工具函数和数据操作
  /store - 状态管理
  /types - 类型定义
```

## 应用架构

```mermaid
graph TD
    A[首页] --> B[作品管理]
    A --> C[提示词管理]
    B --> E[作品编辑器]
    E --> F[AI助手]
    E --> G[章节管理]
    E --> H[知识库]
    C --> I[提示词编辑器]

    H --> K[知识条目创建/编辑]
    H --> L[知识条目分类管理]
    F --> M[AI写作]
    F --> N[AI润色]
    F -.- H
```

## 用户认证

使用 Supabase 实现用户认证功能，支持：

- 邮箱/密码注册和登录
- 用户个人信息管理
- 数据与用户关联，确保数据安全

详细配置和使用说明请参考 [Supabase 集成指南](SUPABASE.md)。

## 下一步计划

1. 导出/导入功能
2. 更多AI模型支持
3. 离线工作模式
4. 完善知识库功能，支持更丰富的关联和引用
5. 完善 Supabase 集成，支持数据同步和多设备访问