/**
 * OTP验证API路由
 * 处理邮箱验证码验证请求
 */
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { handleSupabaseAuthError } from '@/lib/utils/ErrorHandler';

// Supabase配置
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

// 创建Supabase客户端
const supabase = createClient(supabaseUrl, supabaseAnonKey);

/**
 * POST 方法 - 验证邮箱OTP
 */
export async function POST(request: NextRequest) {
  try {
    // 解析请求体
    const body = await request.json();
    const { email, token } = body;

    // 验证请求数据
    if (!email || !token) {
      return NextResponse.json(
        { success: false, message: '邮箱和验证码都是必填项' },
        { status: 400 }
      );
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { success: false, message: '邮箱格式不正确' },
        { status: 400 }
      );
    }

    // 验证OTP格式（6位数字）
    const otpRegex = /^\d{6}$/;
    if (!otpRegex.test(token)) {
      return NextResponse.json(
        { success: false, message: '验证码必须是6位数字' },
        { status: 400 }
      );
    }

    console.log('开始验证OTP:', email, 'token:', token);

    // 使用Supabase验证OTP
    const { data, error } = await supabase.auth.verifyOtp({
      email,
      token,
      type: 'email'
    });

    if (error) {
      console.error('OTP验证失败:', error);

      // 使用统一错误处理
      const errorMessage = handleSupabaseAuthError(error);

      return NextResponse.json(
        { success: false, message: errorMessage },
        { status: 400 }
      );
    }

    if (!data.user) {
      return NextResponse.json(
        { success: false, message: '验证失败，用户不存在' },
        { status: 400 }
      );
    }

    console.log('OTP验证成功:', data.user.email);

    // 返回成功响应，包含会话信息
    return NextResponse.json({
      success: true,
      message: '邮箱验证成功',
      user: data.user,
      session: data.session
    });

  } catch (error) {
    console.error('OTP验证异常:', error);
    return NextResponse.json(
      { success: false, message: '服务器内部错误' },
      { status: 500 }
    );
  }
}

/**
 * PUT 方法 - 重新发送OTP验证码
 */
export async function PUT(request: NextRequest) {
  try {
    // 解析请求体
    const body = await request.json();
    const { email } = body;

    // 验证请求数据
    if (!email) {
      return NextResponse.json(
        { success: false, message: '邮箱是必填项' },
        { status: 400 }
      );
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { success: false, message: '邮箱格式不正确' },
        { status: 400 }
      );
    }

    console.log('重新发送OTP验证码:', email);

    // 重新发送OTP（通过重新注册触发）
    // 注意：这里需要用户提供密码，或者使用其他方式重新发送
    // Supabase没有直接的重发OTP API，需要重新触发注册流程
    const { error } = await supabase.auth.resend({
      type: 'signup',
      email: email
    });

    if (error) {
      console.error('重新发送OTP失败:', error);

      // 使用统一错误处理
      const errorMessage = handleSupabaseAuthError(error);

      return NextResponse.json(
        { success: false, message: errorMessage },
        { status: 400 }
      );
    }

    console.log('OTP验证码重新发送成功:', email);

    return NextResponse.json({
      success: true,
      message: '验证码已重新发送，请查收邮件'
    });

  } catch (error) {
    console.error('重新发送OTP异常:', error);
    return NextResponse.json(
      { success: false, message: '服务器内部错误' },
      { status: 500 }
    );
  }
}
