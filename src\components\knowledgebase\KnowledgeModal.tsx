/**
 * 知识库模态窗口组件
 */
import React, { useState, useEffect } from 'react';
import { Modal } from '@/components/common/modals';
import { Knowledge } from '@/data';
import { getAllKnowledge, getKnowledgeByWorkId, addKnowledge, updateKnowledge, deleteKnowledge } from '@/data';

interface KnowledgeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (knowledge: Knowledge) => void;
  workId?: number; // 添加 workId 参数，指定当前操作的作品
  footer?: React.ReactNode; // 添加可选的自定义footer
  isMultiSelect?: boolean; // 添加多选模式开关
  initialSelectedIds?: number[]; // 添加初始选中的ID列表
}



/**
 * 知识库模态窗口组件
 */
export const KnowledgeModal: React.FC<KnowledgeModalProps> = ({
  isOpen,
  onClose,
  onSelect,
  workId,
  footer,
  isMultiSelect = false, // 默认为单选
  initialSelectedIds = [] // 默认为空数组
}) => {
  // 状态
  const [knowledgeItems, setKnowledgeItems] = useState<Knowledge[]>([]);
  const [selectedKnowledge, setSelectedKnowledge] = useState<Knowledge | null>(null);

  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [editingTitleId, setEditingTitleId] = useState<number | null>(null);
  const [editingTitle, setEditingTitle] = useState('');
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [knowledgeToDelete, setKnowledgeToDelete] = useState<Knowledge | null>(null);

  // 自动保存计时器状态
  const [saveTimeout, setSaveTimeout] = useState<NodeJS.Timeout | null>(null);

  // 将 initialSelectedIds 转换为 Set 以提高查找效率
  const selectedIdsSet = new Set(initialSelectedIds);

  // 加载知识
  const loadKnowledge = async () => {
    setIsLoading(true);
    setError('');

    try {
      let knowledgeData: Knowledge[];

      // 如果有 workId，表示只加载此作品的知识
      if (workId) {
        knowledgeData = await getKnowledgeByWorkId(workId);
      } else {
        // 否则，加载全部知识
        knowledgeData = await getAllKnowledge();
      }

      // 按 updatedAt 降序排序
      knowledgeData.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());

      setKnowledgeItems(knowledgeData);

      // 默认选中第一条（如果列表不为空）
      if (knowledgeData.length > 0) {
        setSelectedKnowledge(knowledgeData[0]);
      } else {
        setSelectedKnowledge(null); // 如果列表为空，则不选中任何项
      }

    } catch (error) {
      console.error('加载知识失败:', error);
      setError('加载知识失败，请稍后再试。');
    } finally {
      setIsLoading(false);
    }
  };

  // 当模态窗口打开时加载知识
  useEffect(() => {
    if (isOpen) {
      loadKnowledge();
      // 关闭时重置状态
      return () => {
        setSelectedKnowledge(null);
        setError('');
        // 清理标题编辑状态
        setEditingTitleId(null);
        setEditingTitle('');
        // 清理搜索词
        setSearchTerm('');
      };
    }
  }, [isOpen, workId]);



  // 清理自动保存计时器
  useEffect(() => {
    // 当组件卸载、模态框关闭或选中的档案切换时，清除计时器
    return () => {
      if (saveTimeout) {
        clearTimeout(saveTimeout);
      }
    };
  }, [isOpen, selectedKnowledge, saveTimeout]); // 依赖项包含isOpen, selectedKnowledge和saveTimeout

  // 处理知识点击
  const handleKnowledgeClick = (knowledge: Knowledge) => {
    setSelectedKnowledge(knowledge);
    // 如果是多选模式，调用 onSelect 通知父组件切换选中状态
    if (isMultiSelect) {
      onSelect(knowledge);
    }
  };

  // 处理知识选择 (单选模式下的按钮)
  const handleSingleKnowledgeSelect = () => {
    if (selectedKnowledge && !isMultiSelect) { // 仅单选模式下有效
      onSelect(selectedKnowledge);
    }
  };



  // 处理知识创建
  const handleCreate = async () => {
    try {
      // 1. 创建新知识对象，标题默认为空
      const newKnowledge: Omit<Knowledge, 'id'> = {
        title: '', // 再次确认：默认标题设置为空字符串
        content: '',
        workId: workId || 0,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // 2. 保存新知识
      const createdKnowledge = await addKnowledge(newKnowledge);

      // 3. 选中新创建的知识并立即进入标题编辑模式
      if (createdKnowledge) {
        await loadKnowledge(); // 先重新加载列表
        setSelectedKnowledge(createdKnowledge); // 再设置选中项
        setEditingTitleId(createdKnowledge.id || null); // 进入标题编辑模式
        setEditingTitle(''); // 编辑框内容为空
      }
    } catch (error) {
      console.error('创建知识失败:', error);
      setError('创建知识失败，请稍后再试。');
    }
  };



  // 处理知识删除
  const handleDelete = async () => {
    if (knowledgeToDelete && knowledgeToDelete.id) {
      try {
        await deleteKnowledge(knowledgeToDelete.id);
        // 如果删除的是当前选中的知识，清除选中状态
        if (selectedKnowledge?.id === knowledgeToDelete.id) {
          setSelectedKnowledge(null);
        }
        await loadKnowledge();
        setShowDeleteConfirm(false);
        setKnowledgeToDelete(null);
      } catch (error) {
        console.error('删除知识失败:', error);
        setError('删除知识失败，请稍后再试。');
      }
    }
  };

  // 保存知识（更新）- 现在接收完整的 Knowledge 对象
  const handleSaveKnowledge = async (knowledgeData: Knowledge & { id: number }) => {
    console.log("[handleSaveKnowledge] Attempting to save:", knowledgeData); // 日志：尝试保存
    try {
      // 直接调用 updateKnowledge
      const updatedKnowledge = await updateKnowledge(knowledgeData);
      console.log("[handleSaveKnowledge] Successfully saved:", updatedKnowledge); // 日志：保存成功

      // 重新加载列表即可，不需要手动更新 selectedKnowledge 或编辑状态
      await loadKnowledge();

      // 如果当前预览的是被保存的知识，可以考虑更新预览状态（可选，因为列表会刷新）
      if (selectedKnowledge?.id === updatedKnowledge.id) {
          setSelectedKnowledge(updatedKnowledge);
      }

    } catch (error) {
      console.error('[handleSaveKnowledge] 保存知识失败:', error); // 日志：保存失败
      setError('保存知识失败，请稍后再试。');
    }
  };





  // 过滤知识
  const filteredKnowledge = knowledgeItems
    // 应用搜索过滤
    .filter(knowledge => {
      if (!searchTerm) return true;

      return (
        knowledge.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        knowledge.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (knowledge.tags && knowledge.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase())))
      );
    });

  // 根据是否有 workId 动态设置标题
  const modalTitle = workId ? "作品知识库" : "全局知识库";

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={modalTitle}
      footer={
        footer || (
          <div className="flex justify-between items-center w-full">
            <div>
              {selectedKnowledge ? (
                <div className="flex items-center">
                  <span className="material-icons text-sm mr-2 text-[rgba(125,133,204,0.7)]">update</span>
                  <span className="text-sm text-text-light">{new Date(selectedKnowledge.updatedAt).toLocaleString()}</span>
                </div>
              ) : (
                <div className="text-sm text-text-medium">
                  {workId !== undefined && (
                    <span className="flex items-center">
                      <span className="material-icons text-sm align-middle mr-1">folder_special</span>
                      当前作品知识库
                    </span>
                  )}
                </div>
              )}
            </div>
            <div className="flex gap-2">
              {/* 条件渲染按钮区域 */}
              {selectedKnowledge && !isMultiSelect ? ( // 单选模式 + 有选中项: 显示"使用此知识条目"
                <button
                  onClick={handleSingleKnowledgeSelect}
                  className="px-4 py-2 bg-[#7D85CC] text-white rounded-lg hover:bg-[#6b73b3] flex items-center"
                >
                  <span className="material-icons text-lg mr-1">check</span> 使用此知识
                </button>
              ) : selectedKnowledge && isMultiSelect ? ( // 多选模式 + 有预览项: 不显示特定按钮 (null)
                 null
              ) : ( // 无选中项 (selectedKnowledge is null): 显示主"关闭"按钮
                <button
                  onClick={onClose}
                  className="px-4 py-2 bg-[#7D85CC] text-white rounded-lg hover:bg-[#6b73b3] flex items-center"
                >
                  关闭
                </button>
              )}
              {/* 始终显示的次要"关闭"按钮 */}
              <button
                onClick={onClose}
                className="px-4 py-2 border border-[rgba(125,133,204,0.3)] rounded-lg text-text-dark hover:bg-[rgba(125,133,204,0.1)]"
              >
                关闭
              </button>
            </div>
          </div>
        )
      }
      maxWidth="max-w-6xl"
    >
      <div className="h-full flex">
        {/* 左侧知识目录 */}
        <div className="w-2/5 pr-4 flex flex-col h-full">
          {/* 顶部搜索和过滤 */}
          <div className="mb-3 flex gap-2">
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="搜索知识..."
              className="flex-1 rounded-lg border border-[rgba(125,133,204,0.3)] px-3 py-2 focus:outline-none focus:border-[#7D85CC]"
            />
            <button
              onClick={handleCreate}
              className="px-3 py-2 flex items-center shrink-0 rounded-lg bg-[#7D85CC] text-white hover:bg-[#6b73b3]"
              title="新建知识"
            >
              <span className="material-icons text-sm mr-1">add</span>
              新建
            </button>
          </div>

          {/* 知识列表 */}
          <div className="scrollable-container">
            {isLoading ? (
              <div className="h-full flex items-center justify-center">
                <span className="text-[#7D85CC]">加载中...</span>
              </div>
            ) : error ? (
              <div className="h-full flex items-center justify-center">
                <span className="text-red-500">{error}</span>
              </div>
            ) : filteredKnowledge.length === 0 ? (
              <div className="h-full flex items-center justify-center flex-col">
                <span className="material-icons text-4xl text-[rgba(125,133,204,0.3)] mb-2">folder_open</span>
                <span className="text-text-light text-center">
                  {workId !== undefined
                    ? '此作品还没有知识'
                    : '没有找到知识'
                  }
                </span>
              </div>
            ) : (
              <div className="space-y-2">
                {filteredKnowledge.map((knowledge) => {
                  const isMultiSelected = isMultiSelect && selectedIdsSet.has(knowledge.id as number);
                  const isCurrentlyEditing = selectedKnowledge?.id === knowledge.id;

                  return (
                    <div
                      key={knowledge.id}
                      className={`group p-3 rounded-lg transition-colors cursor-pointer flex items-center ${
                        isCurrentlyEditing
                        ? 'bg-[#d8ddf2] border-l-4 border-[#7D85CC]' // 当前编辑项的样式 - 不透明
                        : isMultiSelected
                        ? 'bg-[#dfe3f5] border-l-4 border-[#7D85CC]' // 多选选中项的样式 - 不透明
                        : 'bg-[#ebeef8] hover:bg-[#e4e8f6] border-l-4 border-transparent' // 默认使用悬停颜色，悬停时稍微加深
                      }`}
                      onClick={() => {
                        handleKnowledgeClick(knowledge);
                      }}
                    >
                      {/* 多选复选框 - 垂直居中 */}
                      {isMultiSelect && (
                        <div className="flex items-center h-full">
                          <input
                            type="checkbox"
                            checked={isMultiSelected}
                            readOnly // 状态由外部控制，点击整个div触发
                            className="mr-3 h-4 w-4 text-[#7D85CC] focus:ring-[#7D85CC] border-gray-300 rounded cursor-pointer"
                            onClick={(e) => {
                              e.stopPropagation(); // 阻止冒泡
                              handleKnowledgeClick(knowledge); // 点击复选框也触发选中/取消
                            }}
                          />
                        </div>
                      )}
                      {/* 知识信息主体 */}
                      <div className="flex-1 min-w-0">
                        <div className="flex justify-between items-center">
                          {editingTitleId === knowledge.id ? (
                            // 编辑标题的 Input
                            <input
                              type="text"
                              value={editingTitle}
                              onChange={(e) => setEditingTitle(e.target.value)}
                              className="flex-1 px-2 py-1 text-base font-medium rounded border-0 bg-white shadow-sm focus:outline-none focus:ring-1 focus:ring-[#7D85CC]"
                              autoFocus
                              onKeyDown={(e) => {
                                if (e.key === 'Enter' && knowledge.id !== undefined) {
                                  // 检查标题是否为空，为空则使用默认名称
                                  const finalTitle = editingTitle.trim() === '' ? '新知识' : editingTitle.trim();
                                  const updatedKnowledge = { ...knowledge, title: finalTitle, updatedAt: new Date(), id: knowledge.id };
                                  updateKnowledge(updatedKnowledge).then(() => {
                                    // 重新加载列表并清除编辑状态
                                    loadKnowledge().then(() => {
                                      // 更新选中的知识，如果它当前被选中
                                      if (selectedKnowledge?.id === knowledge.id) {
                                        setSelectedKnowledge(prev => prev ? {...prev, title: finalTitle, updatedAt: updatedKnowledge.updatedAt} : null);
                                      }
                                      setEditingTitleId(null);
                                    });
                                  });
                                } else if (e.key === 'Escape') {
                                  setEditingTitleId(null);
                                  // 如果是新建的空标题取消编辑，则删除该知识？ (可选逻辑，暂不实现)
                                  // if (knowledge.title === '') deleteKnowledge(knowledge.id as number).then(loadKnowledge);
                                }
                              }}
                              onBlur={() => {
                                // 检查标题是否为空，为空则使用默认名称
                                const finalTitle = editingTitle.trim() === '' ? '新知识' : editingTitle.trim();
                                if (knowledge.id !== undefined) {
                                  // 只有在标题确实更改过或从默认空变成默认名时才保存
                                  if (finalTitle !== knowledge.title) {
                                    const updatedKnowledge = { ...knowledge, title: finalTitle, updatedAt: new Date(), id: knowledge.id };
                                    updateKnowledge(updatedKnowledge).then(() => {
                                      // 重新加载列表并清除编辑状态
                                      loadKnowledge().then(() => {
                                        // 更新选中的知识，如果它当前被选中
                                        if (selectedKnowledge?.id === knowledge.id) {
                                          setSelectedKnowledge(prev => prev ? {...prev, title: finalTitle, updatedAt: updatedKnowledge.updatedAt} : null);
                                        }
                                        setEditingTitleId(null);
                                      });
                                    });
                                  } else {
                                    // 标题未更改，直接退出编辑模式
                                    setEditingTitleId(null);
                                  }
                                } else {
                                  // ID 不存在，直接退出编辑模式
                                  setEditingTitleId(null);
                                }
                                // 如果是新建的空标题取消编辑，则删除该知识条目？ (可选逻辑，暂不实现)
                                // if (finalTitle === '新知识条目' && knowledge.title === '') deleteKnowledge(knowledge.id as number).then(loadKnowledge);
                              }}
                              onClick={(e) => e.stopPropagation()}
                            />
                          ) : (
                            // 显示标题
                            <div className="flex items-center flex-1 overflow-hidden">
                              <div className="font-medium truncate text-base">
                                {knowledge.title}
                              </div>
                            </div>
                          )}
                          {/* 编辑和删除按钮 */}
                          <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity ml-2 flex-shrink-0">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                setEditingTitleId(knowledge.id || null);
                                setEditingTitle(knowledge.title);
                              }}
                              className="p-1 rounded-full hover:bg-[rgba(125,133,204,0.2)] text-[#7D85CC]"
                              title="重命名"
                            >
                              <span className="material-icons text-sm">edit</span>
                            </button>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                setKnowledgeToDelete(knowledge);
                                setShowDeleteConfirm(true);
                              }}
                              className="p-1 rounded-full hover:bg-[rgba(255,100,100,0.2)] text-red-500"
                              title="删除"
                            >
                              <span className="material-icons text-sm">delete</span>
                            </button>
                          </div>
                        </div>
                        {/* 内容预览和日期 - 仅在非编辑模式下显示 */}
                        {editingTitleId !== knowledge.id && (
                          <div className="relative">
                            {/* 内容预览 */}
                            <div className="text-sm text-text-medium mt-1 line-clamp-2 pr-16">
                              {knowledge.content}
                            </div>

                            {/* 更新日期 - 右下角 */}
                            <div className="text-xs text-text-light absolute bottom-0 right-0">
                              {new Date(knowledge.updatedAt).toLocaleDateString()}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </div>

        {/* 右侧内容/编辑区 */}
        <div className="w-3/5 pl-4 border-l border-[rgba(125,133,204,0.2)] h-full flex flex-col">
          {selectedKnowledge ? (
            <div className="h-full flex flex-col">
              {/* 直接显示内容编辑区，增强美观性 */}
              <div className="flex-1 overflow-y-auto bg-[rgba(250,253,250,0.8)] rounded-lg shadow-sm">
                <textarea
                  value={selectedKnowledge.content}
                  onChange={(e) => {
                    const newContent = e.target.value;
                    const now = new Date();

                    // 1. 更新本地状态
                    const updatedSelected = {
                      ...selectedKnowledge,
                      content: newContent,
                      updatedAt: now
                    };
                    setSelectedKnowledge(updatedSelected);

                    // 2. 清除旧计时器
                    if (saveTimeout) {
                      clearTimeout(saveTimeout);
                    }

                    // 3. 设置新计时器以自动保存
                    const newTimeout = setTimeout(() => {
                      // 确保 updatedSelected 及其 id 存在
                      if (updatedSelected && updatedSelected.id !== undefined) {
                        console.log("[setTimeout] Triggering auto-save for:", updatedSelected.id); // 日志：触发保存
                        // 直接传递包含 id 的 updatedSelected 对象
                        handleSaveKnowledge(updatedSelected as Knowledge & { id: number });
                      } else {
                        console.warn("[setTimeout] Auto-save skipped: Knowledge or ID missing.", updatedSelected); // 日志：跳过保存
                      }
                    }, 1000); // 1秒延迟

                    setSaveTimeout(newTimeout);
                  }}
                  onBlur={() => {
                    // 检查是否有待处理的自动保存
                    if (saveTimeout) {
                      console.log("[onBlur] Pending save detected, saving immediately."); // 日志：检测到待处理保存
                      // 清除计时器
                      clearTimeout(saveTimeout);
                      // 立即保存
                      if (selectedKnowledge && selectedKnowledge.id !== undefined) {
                        handleSaveKnowledge(selectedKnowledge as Knowledge & { id: number });
                      }
                      // 重置计时器状态
                      setSaveTimeout(null);
                    }
                  }}
                  className="w-full h-full rounded-lg px-4 py-3 focus:outline-none border-0 resize-none font-normal text-[14pt] leading-relaxed"
                  placeholder="输入知识内容..."
                  style={{
                    lineHeight: "27px",
                    fontFamily: "'思源黑体', 'Noto Sans SC', sans-serif",
                  }}
                ></textarea>
              </div>
            </div>
          ) : (
            <div className="h-full flex items-center justify-center flex-col bg-[rgba(250,253,250,0.5)] rounded-lg">
              <span className="material-icons text-7xl text-[rgba(125,133,204,0.2)] mb-4">description</span>
              <p className="text-text-light mb-6 text-center">选择一个知识开始编辑</p>
              <button
                onClick={handleCreate}
                className="px-6 py-2 rounded-full flex items-center bg-[#7D85CC] text-white hover:bg-[#6b73b3] shadow-sm hover:shadow-md transition-all transform hover:translate-y-[-1px]"
                title="新建知识"
              >
                <span className="material-icons text-lg mr-1">add</span>
                新建知识
              </button>
            </div>
          )}
        </div>
      </div>

      {/* 删除确认对话框 */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg max-w-md">
            <h3 className="text-lg font-medium text-text-dark mb-4">确认删除</h3>
            <p className="text-text-medium mb-6">确定要删除知识条目 "{knowledgeToDelete?.title}" 吗？此操作无法撤销。</p>
            <div className="flex justify-end space-x-3">
              <button
                className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600"
                onClick={handleDelete}
              >
                删除
              </button>
              <button
                className="px-4 py-2 border border-[rgba(120,180,140,0.3)] text-text-medium rounded-lg hover:bg-[rgba(90,157,107,0.05)]"
                onClick={() => {
                  setShowDeleteConfirm(false);
                  setKnowledgeToDelete(null);
                }}
              >
                取消
              </button>
            </div>
          </div>
        </div>
      )}
    </Modal>
  );
};

export default KnowledgeModal;