/**
 * 用户认证回调API路由
 * 处理邮箱验证回调
 */
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Supabase配置
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

// 强制动态渲染
export const dynamic = 'force-dynamic';

/**
 * GET 方法 - 处理认证回调
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = request.nextUrl;
    const code = searchParams.get('code');

    if (!code) {
      return NextResponse.json(
        { error: '缺少认证代码' },
        { status: 400 }
      );
    }

    // 创建Supabase客户端
    const supabase = createClient(supabaseUrl, supabaseAnonKey);

    // 交换代码获取会话
    const { data, error } = await supabase.auth.exchangeCodeForSession(code);

    if (error) {
      console.error('认证回调失败:', error);
      return NextResponse.json(
        { error: '认证失败' },
        { status: 400 }
      );
    }

    if (!data.session) {
      return NextResponse.json(
        { error: '未能创建会话' },
        { status: 400 }
      );
    }

    console.log('认证回调成功');

    return NextResponse.json({
      success: true,
      message: '邮箱验证成功',
      user: data.user,
      session: data.session,
      access_token: data.session.access_token
    });

  } catch (error) {
    console.error('处理认证回调失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}
