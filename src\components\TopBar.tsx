'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import BackButton from '@/components/BackButton';

interface TopBarProps {
  /**
   * 是否显示返回按钮
   */
  showBackButton?: boolean;

  /**
   * 返回按钮点击回调
   */
  onBackButtonClick?: () => void;

  /**
   * 右侧操作按钮
   */
  actions?: React.ReactNode;
}

/**
 * 通用顶边栏组件
 * 提供统一的页面顶部导航栏
 */
const TopBar: React.FC<TopBarProps> = ({
  showBackButton = false,
  onBackButtonClick,
  actions
}) => {
  const router = useRouter();
  return (
    <div className="sticky top-0 z-50 flex-shrink-0" style={{
      backgroundColor: 'var(--card-color)',
      borderBottom: '1px solid rgba(120,180,140,0.2)',
      boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
      backdropFilter: 'blur(8px)',
      WebkitBackdropFilter: 'blur(8px)'
    }}>
      <div className="py-4 md:py-6 px-4 md:px-8 relative z-10">
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            {showBackButton && (
              onBackButtonClick ? (
                <button
                  onClick={onBackButtonClick}
                  className="p-2 mr-3 rounded-full transition-colors duration-200 flex items-center justify-center"
                  style={{
                    color: 'var(--primary-green)'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = 'rgba(90,157,107,0.15)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }}
                  aria-label="返回"
                >
                  <span className="material-icons">arrow_back</span>
                </button>
              ) : (
                <BackButton />
              )
            )}

            {/* 统一模式：显示功能按钮 */}
            <div className="flex items-center space-x-3">
              <button
                onClick={() => router.push('/')}
                className="flex items-center px-4 py-2 rounded-lg transition-colors duration-200 hover:bg-[rgba(0,0,0,0.05)]"
                style={{ color: 'var(--text-dark)' }}
              >
                <span className="text-xl md:text-2xl lg:text-3xl font-semibold font-ma-shan">首页</span>
              </button>

              <button
                onClick={() => router.push('/prompts')}
                className="flex items-center px-4 py-2 rounded-lg transition-colors duration-200 hover:bg-[rgba(0,0,0,0.05)]"
                style={{ color: 'var(--text-dark)' }}
              >
                <span className="text-xl md:text-2xl lg:text-3xl font-semibold font-ma-shan">提示词库</span>
              </button>

              <button
                onClick={() => router.push('/knowledgebase')}
                className="flex items-center px-4 py-2 rounded-lg transition-colors duration-200 hover:bg-[rgba(0,0,0,0.05)]"
                style={{ color: 'var(--text-dark)' }}
              >
                <span className="text-xl md:text-2xl lg:text-3xl font-semibold font-ma-shan">知识库</span>
              </button>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            {actions}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TopBar;
