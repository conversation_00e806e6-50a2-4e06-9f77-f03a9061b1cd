/**
 * 清空所有本地数据的工具函数
 */
import { resetAllDatabases } from '@/data/database/core/connection';

/**
 * 清空所有localStorage数据
 */
const clearLocalStorage = (): void => {
  if (typeof window === 'undefined') return;
  
  try {
    // 获取所有localStorage键
    const keys = Object.keys(localStorage);
    
    // 清空所有数据
    keys.forEach(key => {
      localStorage.removeItem(key);
    });
    
    console.log('localStorage已清空');
  } catch (error) {
    console.error('清空localStorage失败:', error);
  }
};

/**
 * 清空所有sessionStorage数据
 */
const clearSessionStorage = (): void => {
  if (typeof window === 'undefined') return;
  
  try {
    sessionStorage.clear();
    console.log('sessionStorage已清空');
  } catch (error) {
    console.error('清空sessionStorage失败:', error);
  }
};

/**
 * 清空所有本地数据
 * 包括IndexedDB、localStorage、sessionStorage
 */
export const clearAllLocalData = async (): Promise<void> => {
  if (typeof window === 'undefined') return;
  
  try {
    // 清空IndexedDB数据库
    await resetAllDatabases();
    
    // 清空localStorage
    clearLocalStorage();
    
    // 清空sessionStorage
    clearSessionStorage();
    
    console.log('所有本地数据已清空');
  } catch (error) {
    console.error('清空本地数据失败:', error);
    throw error;
  }
};

/**
 * 带确认对话框的清空所有数据函数
 */
export const clearAllDataWithConfirmation = async (): Promise<boolean> => {
  if (typeof window === 'undefined') return false;
  
  // 第一次确认
  const firstConfirm = confirm(
    '⚠️ 警告：此操作将清空所有本地数据！\n\n' +
    '包括：\n' +
    '• 所有作品和章节\n' +
    '• 知识库和角色卡片\n' +
    '• 聊天记录和AI助手状态\n' +
    '• 用户设置和偏好\n\n' +
    '此操作不可撤销，确定要继续吗？'
  );
  
  if (!firstConfirm) {
    return false;
  }
  
  // 第二次确认
  const secondConfirm = confirm(
    '🔴 最后确认：\n\n' +
    '您确定要删除所有本地数据吗？\n' +
    '这将无法恢复！\n\n' +
    '点击"确定"继续删除，点击"取消"放弃操作。'
  );
  
  if (!secondConfirm) {
    return false;
  }
  
  try {
    await clearAllLocalData();
    
    alert(
      '✅ 所有本地数据已成功清空！\n\n' +
      '页面将自动刷新以完成重置。'
    );
    
    // 延迟刷新页面，让用户看到成功消息
    setTimeout(() => {
      window.location.reload();
    }, 1000);
    
    return true;
  } catch (error) {
    alert(
      '❌ 清空数据时发生错误：\n\n' +
      (error instanceof Error ? error.message : '未知错误') +
      '\n\n请刷新页面后重试。'
    );
    return false;
  }
};
