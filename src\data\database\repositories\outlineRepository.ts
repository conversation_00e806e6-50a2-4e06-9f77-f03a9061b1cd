/**
 * 大纲仓库
 */
import { Outline } from '@/types/outline';
import { dbOperations } from '../core/operations';
import { DB_CONFIG } from '../config';

const { MAIN } = DB_CONFIG.NAMES;
const { OUTLINES } = DB_CONFIG.STORES.MAIN;

/**
 * 添加大纲
 * @param outline 大纲数据
 * @returns 添加后的大纲
 */
export const addOutline = async (outline: Outline): Promise<Outline> => {
  console.log('添加大纲到数据库:', outline);
  return dbOperations.add<Outline>(MAIN, OUTLINES, outline);
};

/**
 * 获取所有大纲
 * @returns 所有大纲
 */
export const getAllOutlines = async (): Promise<Outline[]> => {
  return dbOperations.getAll<Outline>(MAIN, OUTLINES);
};

/**
 * 根据作品ID获取大纲
 * @param workId 作品ID
 * @returns 该作品的所有大纲
 */
export const getOutlinesByWorkId = async (workId: number): Promise<Outline[]> => {
  const allOutlines = await getAllOutlines();
  return allOutlines.filter(outline => outline.workId === workId)
    .sort((a, b) => a.order - b.order);
};

/**
 * 根据ID获取大纲
 * @param id 大纲ID
 * @returns 大纲
 */
export const getOutlineById = async (id: string): Promise<Outline | null> => {
  try {
    return await dbOperations.get<Outline>(MAIN, OUTLINES, id);
  } catch (error) {
    console.error('获取大纲失败:', error);
    return null;
  }
};

/**
 * 更新大纲
 * @param outline 大纲数据
 * @returns 更新后的大纲
 */
export const updateOutline = async (outline: Outline): Promise<Outline> => {
  if (!outline.id) throw new Error('Outline ID is required');
  return dbOperations.update<Outline>(MAIN, OUTLINES, outline);
};

/**
 * 删除大纲
 * @param id 大纲ID
 */
export const deleteOutline = async (id: string): Promise<void> => {
  return dbOperations.remove(MAIN, OUTLINES, id);
};

/**
 * 批量更新大纲顺序
 * @param outlines 大纲数组
 */
export const updateOutlineOrder = async (outlines: Outline[]): Promise<void> => {
  for (const outline of outlines) {
    await updateOutline(outline);
  }
};
